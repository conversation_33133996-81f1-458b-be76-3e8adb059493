import React, { useState } from 'react';
import { z } from 'zod';
import styles from './ChangePassword.module.css';
import CustomInput from '../../common/input/CustomInput';
import toast from 'react-hot-toast';
import useLocalStorage from '../../../hooks/useLocalStorage';
import { useChangePasswordMutation } from '../../../services/userMgtService';

interface ChangePasswordProps {
  onClose: () => void;
}

const passwordSchema = z.object({
  oldPassword: z.string().min(1, 'Old password is required'),
  newPassword: z
    .string()
    .min(8, 'Password must be at least 8 characters long')
    .max(30, 'Password must be less than 30 characters long')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/\d/, 'Password must contain at least one digit')
    .regex(
      /[^A-Za-z0-9]/,
      'Password must contain at least one special character'
    )
    .refine(
      (pwd) => !/\s/.test(pwd),
      'Password must not contain any whitespace characters'
    ),
  confirmPassword: z.string(),
});

const ChangePassword: React.FC<ChangePasswordProps> = ({ onClose }) => {
  const [changePasswordMutation] = useChangePasswordMutation();

  const [oldPassword, setOldPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [user] = useLocalStorage('user', null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      passwordSchema.parse({ oldPassword, newPassword, confirmPassword });
      if (newPassword !== confirmPassword) {
        toast.error("Passwords don't match!");
        return;
      }

      if (oldPassword === newPassword) {
        toast.error('New password is same as old!');
        return;
      }
      const userID = user.id;
      const response: any = await changePasswordMutation({
        userId: userID,
        oldPassword,
        newPassword,
      });

      if (response.data.status == 200) {
        toast.success('Password updated successfully!');
        onClose();
      }
      if (response.data.status == 304) {
        toast.error(response.data.error);
        onClose();
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errorMessages = error.errors.map((err) => err.message);
        toast.error(errorMessages[0]); // Display only the first error message
      } else if (error instanceof Error) {
        toast.error(error.message);
      }
    }
  };

  return (
    <div className={styles.overlay}>
      <div className={styles.modal}>
        <h2>Change Password</h2>
        <form onSubmit={handleSubmit}>
          <div className={styles.formGroup}>
            <CustomInput
              label="Old Password"
              type="password"
              value={oldPassword}
              onChange={(value: string) => setOldPassword(value)}
            />
          </div>
          <div className={styles.formGroup}>
            <CustomInput
              label="New Password"
              type="password"
              value={newPassword}
              onChange={(value: string) => setNewPassword(value)}
            />
          </div>
          <div className={styles.formGroup}>
            <CustomInput
              label="Confirm Password"
              type="password"
              value={confirmPassword}
              onChange={(value: string) => setConfirmPassword(value)}
            />
          </div>
          <button type="submit" className={styles.saveButton}>
            Save Password
          </button>
          <button
            type="button"
            className={styles.cancelButton}
            onClick={onClose}
          >
            Cancel
          </button>
        </form>
      </div>
    </div>
  );
};

export default ChangePassword;
