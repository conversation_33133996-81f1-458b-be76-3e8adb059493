import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import useLocalStorage from '../../../hooks/useLocalStorage';
import {
  CircularProgress,
  Tabs,
  Tab,
  Box,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
} from '@mui/material';
import {
  useSaveToSketchbookMutation,
  useSaveCustomChartsMutation,
  useUpdateSketchbookLayoutMutation,
  useAddPageToSketchbookMutation,
  useGetSketchbooksByUserWithTypeQuery,
  useUseSketchbookTemplateMutation,
  useDeleteSketchbookTemplateMutation,
} from '../../../services/sketchbookServices';
import {
  templateImages,
  templateDescriptions,
} from '../../../utils/sketchbookTemplateImages';
import fallbackTemplateImage from '../../../assets/images/logoIcon.png';
import styles from './SketchbookTemplates.module.css';

// Template type definition
interface Template {
  id: string;
  name: string;
  description: string;
  image: string;
  configuration: {
    pageSize: {
      value: string;
      label: string;
      width: number;
      height: number;
      orientation: 'portrait' | 'landscape';
    };
    pages: Array<{
      charts: Array<{
        type: string;
        title: string;
        position?: { x: number; y: number };
        size?: { width: number; height: number };
        data?: any;
        options?: any;
        gridPosition?: { x: number; y: number; w: number; h: number };
      }>;
    }>;
  };
}

// Define templates
const templates: Template[] = [
  {
    id: 'blank',
    name: 'Blank Canvas',
    description:
      'Start with a clean canvas to create your own layout from scratch',
    image: templateImages.blank,
    configuration: {
      pageSize: {
        value: 'a4',
        label: 'A4',
        width: 559,
        height: 793,
        orientation: 'portrait',
      },
      pages: [{ charts: [] }],
    },
  },
  // Data Analysis Template
  {
    id: 'data-analysis',
    name: 'Data Analysis',
    description:
      'Perfect for creating data visualization reports and analytics dashboards',
    image: templateImages.dataAnalysis,
    configuration: {
      pageSize: {
        value: 'a4',
        label: 'A4',
        width: 559,
        height: 793,
        orientation: 'portrait',
      },
      pages: [
        {
          charts: [
            {
              type: 'textarea',
              title: 'Analysis Title',
              gridPosition: { x: 0, y: 0, w: 12, h: 4 },
              data: {
                text: '# Data Analysis Report\n\nInsights and visualizations',
                fontSize: 18,
                fontFamily: 'Arial',
                textAlign: 'center',
                color: '#000000',
                backgroundColor: 'transparent',
              },
            },
            {
              type: 'scatter',
              title: 'Correlation Analysis',
              gridPosition: { x: 0, y: 2, w: 12, h: 6 },
              data: {
                datasets: [
                  {
                    label: 'Dataset 1',
                    data: [
                      { x: 10, y: 20 },
                      { x: 15, y: 25 },
                      { x: 20, y: 30 },
                      { x: 25, y: 35 },
                      { x: 30, y: 40 },
                      { x: 35, y: 45 },
                      { x: 40, y: 50 },
                      { x: 45, y: 55 },
                      { x: 50, y: 60 },
                    ],
                    backgroundColor: [
                      '#FF6384',
                      '#36A2EB',
                      '#FFCE56',
                      '#4BC0C0',
                      '#9966FF',
                      '#FF9F40',
                      '#FFCD56',
                      '#4BC0C0',
                      '#36A2EB',
                    ],
                  },
                ],
              },
              options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    display: true,
                    position: 'top',
                  },
                  title: {
                    display: true,
                    text: 'Correlation Analysis',
                  },
                },
                scales: {
                  x: {
                    type: 'linear',
                    display: true,
                    title: {
                      display: true,
                      text: 'Variable X',
                    },
                  },
                  y: {
                    type: 'linear',
                    display: true,
                    title: {
                      display: true,
                      text: 'Variable Y',
                    },
                    beginAtZero: true,
                  },
                },
              },
            },
            {
              type: 'radar',
              title: 'Feature Comparison',
              gridPosition: { x: 0, y: 8, w: 12, h: 6 },
              data: {
                labels: [
                  'Feature A',
                  'Feature B',
                  'Feature C',
                  'Feature D',
                  'Feature E',
                ],
                datasets: [
                  {
                    label: 'Product 1',
                    data: [85, 70, 90, 65, 75],
                    backgroundColor: ['rgba(54, 162, 235, 0.2)'],
                    borderColor: 'rgba(54, 162, 235, 0.8)',
                  },
                  {
                    label: 'Product 2',
                    data: [70, 85, 65, 90, 80],
                    backgroundColor: ['rgba(255, 99, 132, 0.2)'],
                    borderColor: 'rgba(255, 99, 132, 0.8)',
                  },
                ],
              },
              options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    display: true,
                    position: 'top',
                  },
                  title: {
                    display: true,
                    text: 'Feature Comparison',
                  },
                },
              },
            },
            {
              type: 'textarea',
              title: 'Insights',
              gridPosition: { x: 0, y: 14, w: 12, h: 4 },
              data: {
                text: '## Key Insights\n\nAdd your analysis and insights based on the data visualizations.',
                fontSize: 14,
                fontFamily: 'Arial',
                textAlign: 'left',
                color: '#000000',
                backgroundColor: 'transparent',
              },
            },
          ],
        },
      ],
    },
  },
  // Business Report Template
  {
    id: 'business-report',
    name: 'Business Report',
    description: templateDescriptions.businessReport,
    image: templateImages.businessReport,
    configuration: {
      pageSize: {
        value: 'a4',
        label: 'A4',
        width: 559,
        height: 793,
        orientation: 'portrait',
      },
      pages: [
        {
          charts: [
            {
              type: 'textarea',
              title: 'Report Title',
              gridPosition: { x: 0, y: 5, w: 12, h: 12 },
              data: {
                text: '# Business Report\n\nThis is a sample business report template. Replace this text with your report content.\n\nUse this section to provide an executive summary of your business report. Include key findings, recommendations, and important metrics that stakeholders should know about.\n\n## Background\n\nProvide context for your report here. Explain why this report was created and what business questions it aims to answer.\n\n## Methodology\n\nDescribe the methods used to collect and analyze the data presented in this report.',
                fontSize: 16,
                fontFamily: 'Arial',
                textAlign: 'left',
                color: '#000000',
                backgroundColor: 'transparent',
              },
            },
            {
              type: 'bar',
              title: 'Revenue by Quarter',
              gridPosition: { x: 0, y: 0, w: 12, h: 5 },
              data: {
                labels: ['Q1', 'Q2', 'Q3', 'Q4'],
                datasets: [
                  {
                    label: 'Revenue',
                    data: [12000, 19000, 15000, 21000],
                    backgroundColor: [
                      'rgba(54, 162, 235, 0.5)',
                      'rgba(54, 162, 235, 0.5)',
                      'rgba(54, 162, 235, 0.5)',
                      'rgba(54, 162, 235, 0.5)',
                    ],
                  },
                ],
              },
              options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    display: true,
                    position: 'top',
                  },
                  title: {
                    display: true,
                    text: 'Revenue by Quarter',
                  },
                },
                scales: {
                  x: {
                    type: 'category',
                    display: true,
                    title: {
                      display: true,
                      text: 'Quarter',
                    },
                  },
                  y: {
                    type: 'linear',
                    display: true,
                    title: {
                      display: true,
                      text: 'Revenue ($)',
                    },
                    beginAtZero: true,
                  },
                },
              },
            },
            {
              type: 'textarea',
              title: 'Analysis',
              gridPosition: { x: 0, y: 14, w: 12, h: 11 },
              data: {
                text: '## Analysis\n\nThe chart above shows quarterly revenue. Add your analysis here.\n\n### Key Findings\n\n- Q2 shows the highest revenue at $19,000\n- Q4 shows strong growth compared to Q3\n- Overall trend is positive with some fluctuation\n\n### Recommendations\n\n1. Investigate factors contributing to Q2 success\n2. Apply successful Q2 strategies to other quarters\n3. Develop plan to reduce quarterly fluctuations',
                fontSize: 14,
                fontFamily: 'Arial',
                textAlign: 'left',
                color: '#000000',
                backgroundColor: 'transparent',
              },
            },
          ],
        },
      ],
    },
  },
  // Financial Dashbaord Template
  {
    id: 'financial-dashboard',
    name: 'Financial Dashboard',
    description: templateDescriptions.financialDashboard,
    image: templateImages.financialDashboard,
    configuration: {
      pageSize: {
        value: 'a4',
        label: 'A4',
        width: 559,
        height: 793,
        orientation: 'portrait',
      },
      pages: [
        {
          charts: [
            {
              type: 'textarea',
              title: 'Dashboard Title',
              gridPosition: { x: 0, y: 0, w: 12, h: 6 },
              data: {
                text: '# Financial Dashboard\n\nKey metrics and performance indicators\n\nThis dashboard provides an overview of your financial performance, including revenue distribution, monthly trends, target achievement, and quarterly performance metrics. Use this dashboard to monitor key financial indicators and make data-driven decisions.',
                fontSize: 18,
                fontFamily: 'Arial',
                textAlign: 'center',
                color: '#000000',
                backgroundColor: 'transparent',
              },
            },
            {
              type: 'pie',
              title: 'Revenue Distribution',
              gridPosition: { x: 4, y: 6, w: 8, h: 10 },
              data: {
                labels: ['Product A', 'Product B', 'Product C', 'Product D'],
                datasets: [
                  {
                    data: [35, 25, 20, 20],
                    backgroundColor: [
                      'rgba(54, 162, 235, 0.5)',
                      'rgba(255, 99, 132, 0.5)',
                      'rgba(255, 206, 86, 0.5)',
                      'rgba(75, 192, 192, 0.5)',
                    ],
                  },
                ],
              },
              options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    display: true,
                    position: 'top',
                  },
                  title: {
                    display: true,
                    text: 'Revenue Distribution',
                  },
                },
              },
            },
            {
              type: 'line',
              title: 'Monthly Trends',
              gridPosition: { x: 0, y: 16, w: 12, h: 11 },
              data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [
                  {
                    label: 'Revenue',
                    data: [12000, 19000, 15000, 21000, 18000, 25000],
                    borderColor: 'rgba(54, 162, 235, 0.8)',
                    backgroundColor: ['rgba(54, 162, 235, 0.1)'],
                    tension: 0.4,
                  },
                  {
                    label: 'Expenses',
                    data: [8000, 12000, 10000, 14000, 11000, 16000],
                    borderColor: 'rgba(255, 99, 132, 0.8)',
                    backgroundColor: ['rgba(255, 99, 132, 0.1)'],
                    tension: 0.4,
                  },
                ],
              },
              options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    display: true,
                    position: 'top',
                  },
                  title: {
                    display: true,
                    text: 'Monthly Trends',
                  },
                },
                scales: {
                  x: {
                    display: true,
                    title: {
                      display: true,
                      text: 'Month',
                    },
                  },
                  y: {
                    display: true,
                    title: {
                      display: true,
                      text: 'Amount ($)',
                    },
                    beginAtZero: true,
                  },
                },
              },
            },
            {
              type: 'gauge',
              title: 'Target Achievement',
              gridPosition: { x: 0, y: 6, w: 4, h: 10 },
              data: {
                value: 75,
              },
              options: {
                responsive: true,
                maintainAspectRatio: false,
                nrOfLevels: 20,
                colors: ['#FF5F6D', '#FFC371'],
                arcWidth: 0.3,
                arcPadding: 0.05,
                cornerRadius: 6,
                needleColor: '#464A4F',
                textColor: '#464A4F',
                animate: true,
                animDelay: 0,
                animateDuration: 2000,
              },
            },
            {
              type: 'bar',
              title: 'Quarterly Performance',
              gridPosition: { x: 0, y: 27, w: 12, h: 11 },
              data: {
                labels: ['Q1', 'Q2', 'Q3', 'Q4'],
                datasets: [
                  {
                    label: 'Actual',
                    data: [85, 90, 78, 95],
                    backgroundColor: [
                      'rgba(54, 162, 235, 0.5)',
                      'rgba(54, 162, 235, 0.5)',
                      'rgba(54, 162, 235, 0.5)',
                      'rgba(54, 162, 235, 0.5)',
                    ],
                  },
                  {
                    label: 'Target',
                    data: [80, 85, 80, 90],
                    backgroundColor: [
                      'rgba(255, 99, 132, 0.5)',
                      'rgba(255, 99, 132, 0.5)',
                      'rgba(255, 99, 132, 0.5)',
                      'rgba(255, 99, 132, 0.5)',
                    ],
                  },
                ],
              },
              options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    display: true,
                    position: 'top',
                  },
                  title: {
                    display: true,
                    text: 'Quarterly Performance',
                  },
                },
                scales: {
                  x: {
                    display: true,
                    title: {
                      display: true,
                      text: 'Quarter',
                    },
                  },
                  y: {
                    display: true,
                    title: {
                      display: true,
                      text: 'Performance (%)',
                    },
                    beginAtZero: true,
                  },
                },
              },
            },
          ],
        },
      ],
    },
  },
  {
    id: 'project-timeline',
    name: 'Project Timeline',
    description: templateDescriptions.projectTimeline,
    image: templateImages.projectTimeline,
    configuration: {
      pageSize: {
        value: 'custom',
        label: 'Custom Size',
        width: 1300,
        height: 793,
        orientation: 'portrait',
      },
      pages: [
        {
          charts: [
            {
              type: 'gantt',
              title: 'Project Gantt Chart',
              gridPosition: { x: 0, y: 0, w: 12, h: 8 },
              data: {
                tasks: [
                  {
                    id: 1,
                    name: 'Planning Phase',
                    start: new Date(
                      new Date().setDate(new Date().getDate() - 10)
                    ),
                    end: new Date(new Date().setDate(new Date().getDate() + 5)),
                    progress: 80,
                  },
                  {
                    id: 2,
                    name: 'Design Phase',
                    start: new Date(
                      new Date().setDate(new Date().getDate() + 6)
                    ),
                    end: new Date(
                      new Date().setDate(new Date().getDate() + 20)
                    ),
                    progress: 30,
                  },
                  {
                    id: 3,
                    name: 'Development Phase',
                    start: new Date(
                      new Date().setDate(new Date().getDate() + 21)
                    ),
                    end: new Date(
                      new Date().setDate(new Date().getDate() + 40)
                    ),
                    progress: 0,
                  },
                  {
                    id: 4,
                    name: 'Testing Phase',
                    start: new Date(
                      new Date().setDate(new Date().getDate() + 41)
                    ),
                    end: new Date(
                      new Date().setDate(new Date().getDate() + 50)
                    ),
                    progress: 0,
                  },
                  {
                    id: 5,
                    name: 'Deployment',
                    start: new Date(
                      new Date().setDate(new Date().getDate() + 51)
                    ),
                    end: new Date(
                      new Date().setDate(new Date().getDate() + 55)
                    ),
                    progress: 0,
                  },
                ],
              },
              options: {
                responsive: true,
                maintainAspectRatio: false,
                viewMode: 'Week',
                columnWidth: 60,
                rowHeight: 40,
                barCornerRadius: 3,
                barProgressColor: '#a3a3ff',
                barProgressSelectedColor: '#8282f5',
                barBackgroundColor: '#b8c2cc',
                barBackgroundSelectedColor: '#aeb8c2',
                handleWidth: 8,
                timeStep: 24 * 60 * 60 * 1000,
                arrowColor: '#333',
                fontFamily: 'Arial',
                fontSize: '12px',
                titleFontSize: '14px',
                gridLines: true,
                dateFormat: 'YYYY-MM-DD',
                locale: 'en-US',
              },
            },
            {
              type: 'textarea',
              title: 'Project Overview',
              gridPosition: { x: 0, y: 8, w: 12, h: 15 },
              data: {
                text: '# Project Timeline\n\nTrack your project milestones and progress\n\n## Project Overview\n\nProject Name: [Project Name]\nStart Date: [Start Date]\nEnd Date: [End Date]\nProject Manager: [PM Name]\nBudget: [Budget Amount]\n\n## Project Description\n\nThis template provides a comprehensive view of your project schedule with a Gantt chart at the top for visualizing timelines and dependencies. The Gantt chart shows the planned timeline for each phase of the project, including planning, design, development, testing, and deployment.\n\n## Project Objectives\n\n1. [Objective 1]\n2. [Objective 2]\n3. [Objective 3]',
                fontSize: 16,
                fontFamily: 'Arial',
                textAlign: 'left',
                color: '#000000',
                backgroundColor: 'transparent',
              },
            },
            {
              type: 'textarea',
              title: 'Project Notes',
              gridPosition: { x: 0, y: 23, w: 12, h: 29 },
              data: {
                text: '## Project Details\n\n### Key Milestones\n* Milestone 1: Project Kickoff - [Date]\n* Milestone 2: Design Approval - [Date]\n* Milestone 3: Development Complete - [Date]\n* Milestone 4: Testing Complete - [Date]\n* Milestone 5: Project Launch - [Date]\n\n### Team Members\n* Project Manager: [Name]\n* Designer: [Name]\n* Developer: [Name]\n* QA Tester: [Name]\n\n### Risks and Mitigation\n| Risk | Impact | Probability | Mitigation Strategy |\n|------|--------|------------|---------------------|\n| Resource unavailability | High | Medium | Cross-train team members |\n| Scope creep | High | High | Strict change management process |\n| Technical challenges | Medium | Medium | Early prototyping and research |\n| Budget constraints | High | Medium | Regular financial reviews |\n| Stakeholder alignment | Medium | High | Regular stakeholder meetings |\n\n### Dependencies\n1. [Dependency 1] - Required for [Task/Phase]\n2. [Dependency 2] - Required for [Task/Phase]\n3. [Dependency 3] - Required for [Task/Phase]\n\n### Action Items\n- [ ] Complete project charter\n- [ ] Finalize resource allocation\n- [ ] Set up development environment\n- [ ] Schedule weekly status meetings\n- [ ] Create communication plan\n- [ ] Establish reporting cadence',
                fontSize: 14,
                fontFamily: 'Arial',
                textAlign: 'left',
                color: '#000000',
                backgroundColor: 'transparent',
              },
            },
          ],
        },
      ],
    },
  },
];

interface SketchbookTemplatesProps {
  onClose?: () => void;
}

// Custom template interface
interface CustomTemplate {
  id: string;
  sketchbook_name: string;
  updatedOnTimestamp: number;
  createdOnTimestamp: number;
}

// Template card for built-in templates
const TemplateCard: React.FC<{
  template: Template;
  onClick: () => void;
  isCreating: boolean;
}> = ({ template, onClick, isCreating }) => {
  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div
      className={`${styles.templateCard} ${isCreating ? styles.disabled : ''}`}
      onClick={onClick}
    >
      {isCreating && (
        <div className={styles.loadingOverlay}>
          <CircularProgress size={40} />
        </div>
      )}
      <div className={styles.templateImage}>
        <img
          src={imageError ? fallbackTemplateImage : template.image}
          alt={template.name}
          onError={handleImageError}
          loading="lazy"
        />
      </div>
      <div className={styles.templateInfo}>
        <h3>{template.name}</h3>
        <p>{template.description}</p>
      </div>
    </div>
  );
};

// Template card for custom templates
const CustomTemplateCard: React.FC<{
  template: CustomTemplate;
  onClick: () => void;
  onDelete: (templateId: string) => void;
  isCreating: boolean;
  isDeleting: boolean;
}> = ({ template, onClick, onDelete, isCreating, isDeleting }) => {
  // Format date for display
  const formatDate = (timestamp: number) => {
    if (!timestamp) return 'Unknown date';
    const date = new Date(timestamp);
    return date.toLocaleDateString();
  };

  // Handle delete button click
  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering the card's onClick
    onDelete(template.id);
  };

  return (
    <div
      className={`${styles.templateCard} ${styles.customTemplateCard} ${isCreating || isDeleting ? styles.disabled : ''}`}
      onClick={onClick}
    >
      {isCreating && (
        <div className={styles.loadingOverlay}>
          <CircularProgress size={40} />
        </div>
      )}
      {isDeleting && (
        <div className={styles.loadingOverlay}>
          <CircularProgress size={40} />
          <Typography variant="body2" sx={{ mt: 1 }}>
            Deleting...
          </Typography>
        </div>
      )}
      <div className={styles.templateImage}>
        <img
          src={fallbackTemplateImage}
          alt={template.sketchbook_name}
          loading="lazy"
        />
      </div>
      <div className={styles.templateInfo}>
        <h3 title={template.sketchbook_name}>{template.sketchbook_name}</h3>
        <p>Created: {formatDate(template.createdOnTimestamp)}</p>
        <button
          className={styles.deleteButton}
          onClick={handleDeleteClick}
          disabled={isCreating || isDeleting}
        >
          Delete
        </button>
      </div>
    </div>
  );
};

const SketchbookTemplates: React.FC<SketchbookTemplatesProps> = ({
  onClose,
}) => {
  const navigate = useNavigate();
  const [userDetails] = useLocalStorage('user', null);
  const userId = userDetails?.id;
  const [saveToSketchbook] = useSaveToSketchbookMutation();
  const [saveCustomChart] = useSaveCustomChartsMutation();
  const [updateSketchbookLayout] = useUpdateSketchbookLayoutMutation();
  const [addPageToSketchbook] = useAddPageToSketchbookMutation();
  const [isCreating, setIsCreating] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [customTemplates, setCustomTemplates] = useState<CustomTemplate[]>([]);
  const [isLoadingCustomTemplates, setIsLoadingCustomTemplates] =
    useState(false);
  const [deletingTemplateId, setDeletingTemplateId] = useState<string | null>(
    null
  );
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [templateToDelete, setTemplateToDelete] = useState<string | null>(null);

  // Fetch custom templates using the new API with type parameter
  const { data: customTemplatesData, isLoading: isCustomTemplatesLoading } =
    useGetSketchbooksByUserWithTypeQuery(
      { userId, page: 1, size: 50, type: 'template' },
      { skip: !userId }
    );

  // Update custom templates when data is loaded
  useEffect(() => {
    if (customTemplatesData?.data?.content) {
      setCustomTemplates(customTemplatesData.data.content);
    }
    setIsLoadingCustomTemplates(isCustomTemplatesLoading);
  }, [customTemplatesData, isCustomTemplatesLoading]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleTemplateSelect = async (template: Template) => {
    if (isCreating) return; // Prevent multiple clicks

    setIsCreating(true);
    const toastId = 'creating-template';
    toast.loading('Creating sketchbook from template...', { id: toastId });

    try {
      await createSketchbookFromBuiltInTemplate(template, toastId);
    } catch (error) {
      console.error('Error creating sketchbook from template:', error);
      toast.dismiss(toastId);
      toast.error('Failed to create sketchbook from template');
    } finally {
      setIsCreating(false);
    }
  };

  const [useSketchbookTemplate] = useUseSketchbookTemplateMutation();
  const [deleteSketchbookTemplate] = useDeleteSketchbookTemplateMutation();

  const handleCustomTemplateSelect = async (template: CustomTemplate) => {
    if (isCreating) return; // Prevent multiple clicks

    setIsCreating(true);
    const toastId = 'creating-template';
    toast.loading('Creating sketchbook from template...', { id: toastId });

    try {
      // Use the new API to create a sketchbook from template
      const payload = {
        templateId: template.id,
        sketchbookName: `${template.sketchbook_name} Copy`,
        projectId: null,
      };

      const response = await useSketchbookTemplate(payload).unwrap();

      if (response.success) {
        const sketchbookId = response.data.id;
        toast.dismiss(toastId);
        toast.success(`Created new sketchbook from template`);

        // Navigate to the sketchbook page
        setTimeout(() => {
          navigate('/sketchbook', { state: { sketchbookId } });
          if (onClose) {
            onClose();
          }
        }, 500);
      } else {
        throw new Error(response.message || 'Failed to create sketchbook');
      }
    } catch (error) {
      console.error('Error creating sketchbook from custom template:', error);
      toast.dismiss(toastId);
      toast.error('Failed to create sketchbook from custom template');
    } finally {
      setIsCreating(false);
    }
  };

  // Handle template delete confirmation
  const handleDeleteConfirm = (templateId: string) => {
    setTemplateToDelete(templateId);
    setShowDeleteConfirm(true);
  };

  // Handle template deletion
  const handleDeleteTemplate = async () => {
    if (!templateToDelete) return;

    setDeletingTemplateId(templateToDelete);
    setShowDeleteConfirm(false);

    const toastId = toast.loading('Deleting template...');

    try {
      const response =
        await deleteSketchbookTemplate(templateToDelete).unwrap();

      toast.dismiss(toastId);

      if (response.success) {
        toast.success('Template deleted successfully');
        // Update the templates list by removing the deleted template
        setCustomTemplates((prevTemplates) =>
          prevTemplates.filter((template) => template.id !== templateToDelete)
        );
      } else {
        toast.error(response.message || 'Failed to delete template');
      }
    } catch (error) {
      console.error('Error deleting template:', error);
      toast.dismiss(toastId);
      toast.error('Failed to delete template');
    } finally {
      setDeletingTemplateId(null);
      setTemplateToDelete(null);
    }
  };

  // For built-in templates, we'll use the existing approach
  const createSketchbookFromBuiltInTemplate = async (
    template: Template,
    toastId: string
  ) => {
    try {
      // Set project ID to null for all templates
      const projectId = null;
      console.log(
        `Creating sketchbook from template: ${template.id} (projectId: ${projectId})`
      );
      let sketchbookId: string;

      // Step 1: Create the initial sketchbook with empty pages
      const initialSketchbookPayload = {
        sketchbook_name: template.name,
        project_id: projectId,
        user_id: userId,
        flowNodes: [],
        flowEdges: [],
        pageEnabled: true,
        activePage: '01',
        pageSize: template.configuration.pageSize,
        // Ensure we have at least one page
        pages:
          template.configuration.pages.length > 0
            ? template.configuration.pages.map((_, index) => ({
                id: `0${index + 1}`,
                name: `0${index + 1}`,
                custom_chart_ids: [],
                ai_chart_ids: [],
                ai_image_ids: [],
                ai_summary_ids: [],
                layouts: [],
              }))
            : [
                {
                  id: '01',
                  name: '01',
                  custom_chart_ids: [],
                  ai_chart_ids: [],
                  ai_image_ids: [],
                  ai_summary_ids: [],
                  layouts: [],
                },
              ],
      };

      console.log(
        'Creating initial sketchbook with payload:',
        initialSketchbookPayload
      );

      // Save the initial sketchbook
      const sketchbookResponse = await saveToSketchbook(
        initialSketchbookPayload
      ).unwrap();

      console.log('Sketchbook creation response:', sketchbookResponse);

      if (sketchbookResponse.status === 201) {
        sketchbookId = sketchbookResponse.data.id;

        // For blank template, we don't need to add an extra page since it's already included in the payload
        // Only add a page for other templates if verification shows it's needed
        if (template.id !== 'blank') {
          console.log(
            'Template is not blank, will verify if page needs to be added'
          );

          // Explicitly add the first page to ensure it exists for non-blank templates
          const firstPagePayload = {
            id: '01',
            name: '01',
            custom_chart_ids: [],
            ai_chart_ids: [],
            ai_image_ids: [],
            ai_summary_ids: [],
            layouts: [],
          };

          try {
            console.log(
              'Adding initial page to ensure it exists:',
              firstPagePayload
            );
            // const addPageResponse = await addPageToSketchbook({
            //   payload: firstPagePayload,
            //   id: sketchbookId,
            // }).unwrap();
            // console.log('Add page response:', addPageResponse);
          } catch (pageError) {
            console.error(
              'Error adding initial page, continuing with template creation:',
              pageError
            );
          }
        } else {
          console.log(
            'Blank template selected, skipping additional page creation'
          );
        }

        // Step 2: Create each custom chart for each page
        for (
          let pageIndex = 0;
          pageIndex < template.configuration.pages.length;
          pageIndex++
        ) {
          const page = template.configuration.pages[pageIndex];
          const layouts = [];

          // Create each chart in the page
          for (
            let chartIndex = 0;
            chartIndex < page.charts.length;
            chartIndex++
          ) {
            const chart = page.charts[chartIndex];

            try {
              // Prepare chart payload based on chart type
              const chartPayload = {
                type: chart.type,
                pageIndex: pageIndex,
                sketchbookId: sketchbookId,
                pageId: `0${pageIndex + 1}`, // Add pageId to match manual drop format
                title:
                  chart.title ||
                  `${chart.type.charAt(0).toUpperCase() + chart.type.slice(1)} Chart`,
              };

              // Add specific properties based on chart type
              if (chart.type === 'textarea') {
                Object.assign(chartPayload, {
                  data: {
                    markdown: chart.data.text || 'Enter your text here...',
                    style: chart.data.style || {
                      fontSize: '14px',
                      lineHeight: '1.5',
                      padding: '12px',
                      border: '1px solid #ccc',
                      backgroundColor: 'transparent',
                    },
                  },
                  options: chart.options || {},
                });
              } else if (chart.type === 'gauge') {
                Object.assign(chartPayload, {
                  datasets: {
                    data: chart.data.value / 100 || 0.75,
                  },
                  options: chart.options || {
                    nrOfLevels: 20,
                    colors: ['#FF5F6D', '#FFC371'],
                    arcWidth: 0.3,
                    arcPadding: 0.05,
                    cornerRadius: 6,
                    needleColor: '#464A4F',
                    textColor: '#464A4F',
                    animate: true,
                    animDelay: 0,
                    animateDuration: 2000,
                  },
                });
              } else if (chart.type === 'gantt') {
                Object.assign(chartPayload, {
                  tasks: chart.data.tasks || [
                    {
                      id: 'task1',
                      name: 'Task 1',
                      start: new Date(Date.now()).toISOString(),
                      end: new Date(
                        Date.now() + 7 * 24 * 60 * 60 * 1000
                      ).toISOString(),
                      progress: 0,
                      dependencies: [],
                    },
                    {
                      id: 'task2',
                      name: 'Task 2',
                      start: new Date(
                        Date.now() + 3 * 24 * 60 * 60 * 1000
                      ).toISOString(),
                      end: new Date(
                        Date.now() + 14 * 24 * 60 * 60 * 1000
                      ).toISOString(),
                      progress: 0,
                      dependencies: ['task1'],
                    },
                  ],
                  options: chart.options || {
                    viewMode: 'Week',
                    viewDate: new Date().toISOString(),
                    columnWidth: 60,
                    rowHeight: 40,
                    barCornerRadius: 3,
                    barProgressColor: '#a3a3ff',
                    barProgressSelectedColor: '#8282f5',
                    barBackgroundColor: '#b8c2cc',
                    barBackgroundSelectedColor: '#aeb8c2',
                    handleWidth: 8,
                    timeStep: 24 * 60 * 60 * 1000,
                    arrowColor: '#333',
                    fontFamily: 'Arial',
                    fontSize: '12px',
                    titleFontSize: '14px',
                    gridLines: true,
                    dateFormat: 'YYYY-MM-DD',
                    locale: 'en-US',
                  },
                });
              } else if (chart.type === 'scatter') {
                // For scatter charts, match the manual drop format
                Object.assign(chartPayload, {
                  labels: chart.data.labels || [
                    'Point 1',
                    'Point 2',
                    'Point 3',
                    'Point 4',
                  ],
                  datasets: chart.data.datasets
                    ? chart.data.datasets.map((dataset: any) => ({
                        ...dataset,
                        backgroundColor: Array.isArray(dataset.backgroundColor)
                          ? dataset.backgroundColor
                          : ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'],
                      }))
                    : [],
                  options: chart.options || {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        display: true,
                        position: 'top',
                      },
                      title: {
                        display: true,
                        text: chart.title || 'Scatter Chart',
                      },
                    },
                    scales: {
                      x: {
                        type: 'category',
                        display: true,
                        title: {
                          display: true,
                          text: 'X Axis',
                        },
                      },
                      y: {
                        type: 'linear',
                        display: true,
                        title: {
                          display: true,
                          text: 'Y Axis',
                        },
                        beginAtZero: true,
                      },
                    },
                  },
                });
              } else if (chart.type === 'bubble') {
                // For bubble charts, match the manual drop format
                Object.assign(chartPayload, {
                  labels: chart.data.labels || [
                    'Point 1',
                    'Point 2',
                    'Point 3',
                  ],
                  datasets: chart.data.datasets
                    ? chart.data.datasets.map((dataset: any) => ({
                        ...dataset,
                        backgroundColor: Array.isArray(dataset.backgroundColor)
                          ? dataset.backgroundColor
                          : ['#FF6384', '#36A2EB', '#FFCE56'],
                      }))
                    : [],
                  options: chart.options || {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        display: true,
                        position: 'top',
                      },
                      title: {
                        display: true,
                        text: chart.title || 'Bubble Chart',
                      },
                    },
                    scales: {
                      x: {
                        type: 'linear',
                        display: true,
                        title: {
                          display: true,
                          text: 'X Axis',
                        },
                      },
                      y: {
                        type: 'linear',
                        display: true,
                        title: {
                          display: true,
                          text: 'Y Axis',
                        },
                        beginAtZero: true,
                      },
                    },
                  },
                });
              } else {
                // For standard charts (bar, line, pie, etc.)
                Object.assign(chartPayload, {
                  labels: chart.data.labels || [],
                  datasets: chart.data.datasets
                    ? chart.data.datasets.map((dataset: any) => ({
                        ...dataset,
                        backgroundColor: Array.isArray(dataset.backgroundColor)
                          ? dataset.backgroundColor
                          : [
                              dataset.backgroundColor ||
                                'rgba(54, 162, 235, 0.5)',
                            ],
                        borderColor:
                          dataset.borderColor || 'rgba(54, 162, 235, 1)',
                      }))
                    : [],
                  options: chart.options || {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        display: true,
                        position: 'top',
                      },
                      title: {
                        display: true,
                        text:
                          chart.title ||
                          `${chart.type.charAt(0).toUpperCase() + chart.type.slice(1)} Chart`,
                      },
                    },
                    scales: {
                      x: {
                        type: 'category',
                        display: true,
                        title: {
                          display: true,
                          text: 'X Axis',
                        },
                      },
                      y: {
                        type: 'linear',
                        display: true,
                        title: {
                          display: true,
                          text: 'Y Axis',
                        },
                        beginAtZero: true,
                      },
                    },
                  },
                });
              }

              // Save the custom chart without showing individual toasts
              try {
                // Calculate grid dimensions based on orientation
                const gridCols =
                  template.configuration.pageSize.orientation === 'portrait'
                    ? 12
                    : 20;
                const gridRows =
                  template.configuration.pageSize.orientation === 'portrait'
                    ? 20
                    : 12;

                // Use gridPosition if available, otherwise calculate from pixel coordinates
                let layoutItem;

                if (chart.gridPosition) {
                  // Use the predefined grid position
                  layoutItem = {
                    i: `chart-${Date.now()}`,
                    x: chart.gridPosition.x,
                    y: chart.gridPosition.y,
                    w: chart.gridPosition.w,
                    h: chart.gridPosition.h,
                  };
                  console.log(
                    `Using predefined grid position for ${chart.title}:`,
                    layoutItem
                  );
                } else if (chart.position && chart.size) {
                  // Calculate grid position based on pixel coordinates
                  layoutItem = {
                    i: `chart-${Date.now()}`,
                    x: Math.min(
                      Math.max(
                        0,
                        Math.floor(
                          chart.position.x /
                            (template.configuration.pageSize.width / gridCols)
                        )
                      ),
                      gridCols - 1
                    ),
                    y: Math.max(
                      0,
                      Math.floor(
                        chart.position.y /
                          (template.configuration.pageSize.height / gridRows)
                      )
                    ),
                    w: Math.min(
                      Math.max(
                        3,
                        Math.ceil(
                          chart.size.width /
                            (template.configuration.pageSize.width / gridCols)
                        )
                      ),
                      gridCols
                    ),
                    h: Math.min(
                      Math.max(
                        2,
                        Math.ceil(
                          chart.size.height /
                            (template.configuration.pageSize.height / gridRows)
                        )
                      ),
                      gridRows * 2
                    ),
                  };
                  console.log(
                    `Calculated grid position for ${chart.title}:`,
                    layoutItem
                  );
                } else {
                  // Default fallback if neither gridPosition nor position/size are provided
                  layoutItem = {
                    i: `chart-${Date.now()}`,
                    x: 0,
                    y: 0,
                    w: 3,
                    h: 2,
                  };
                  console.log(
                    `Using default grid position for ${chart.title}:`,
                    layoutItem
                  );
                }

                const chartPayloadWithLayouts = {
                  ...chartPayload,
                  layouts: [layoutItem],
                };

                const chartResponse = await saveCustomChart({
                  payload: chartPayloadWithLayouts,
                  chartType: chart.type,
                }).unwrap();

                if (chartResponse.status === 201) {
                  console.log(
                    `Chart created successfully: ${chart.type}`,
                    chartResponse.data
                  );

                  // Add the chart to layouts for this page
                  // Calculate grid positions based on the page size
                  const gridCols = 12; // Standard grid columns
                  const gridRows = Math.floor(
                    template.configuration.pageSize.height / 10
                  );

                  // Calculate grid positions with proper scaling
                  let x = 0,
                    y = 0,
                    w = 3,
                    h = 2;

                  if (chart.position && chart.size) {
                    x = Math.floor(
                      chart.position.x /
                        (template.configuration.pageSize.width / gridCols)
                    );
                    y = Math.floor(
                      chart.position.y /
                        (template.configuration.pageSize.height / gridRows)
                    );
                    w = Math.ceil(
                      chart.size.width /
                        (template.configuration.pageSize.width / gridCols)
                    );
                    h = Math.ceil(
                      chart.size.height /
                        (template.configuration.pageSize.height / gridRows)
                    );
                  } else if (chart.gridPosition) {
                    // If gridPosition is available, use it directly
                    x = chart.gridPosition.x;
                    y = chart.gridPosition.y;
                    w = chart.gridPosition.w;
                    h = chart.gridPosition.h;
                  }

                  console.log(`Chart ${chart.title} grid position:`, {
                    x,
                    y,
                    w,
                    h,
                  });

                  layouts.push({
                    i: chartResponse.data.id,
                    x: Math.min(x, gridCols - 1),
                    y: Math.max(0, y),
                    w: Math.min(Math.max(1, w), gridCols),
                    h: Math.max(1, h),
                  });
                } else {
                  console.error(
                    `Failed to create chart: ${chart.type}`,
                    chartResponse
                  );
                }
              } catch (innerChartError) {
                console.error(
                  `Error creating ${chart.type} chart:`,
                  innerChartError
                );
                // Continue with other charts even if one fails
              }
            } catch (chartError) {
              console.error(
                `Error creating chart ${chartIndex} on page ${pageIndex}:`,
                chartError
              );
              // Continue with other charts even if one fails
            }
          }

          // Step 3: Update the sketchbook layout for this page
          if (layouts.length > 0) {
            try {
              console.log(`Updating layout for page ${pageIndex}:`, layouts);
              // Update layout without showing individual toasts
              const layoutResponse = await updateSketchbookLayout({
                id: sketchbookId,
                pageIndex: pageIndex,
                layouts: layouts,
                pageId: `0${pageIndex + 1}`, // Ensure we're sending the correct page ID (01, 02, etc.)
              }).unwrap();

              console.log(
                `Layout update response for page ${pageIndex}:`,
                layoutResponse
              );

              if (!layoutResponse || layoutResponse.status !== 200) {
                console.error(
                  `Layout update failed for page ${pageIndex}:`,
                  layoutResponse
                );
              }
            } catch (layoutError) {
              console.error(
                `Error updating layout for page ${pageIndex}:`,
                layoutError
              );
              // Continue with other pages even if layout update fails
            }
          } else {
            console.warn(`No layouts to update for page ${pageIndex}`);
          }
        }

        // Verify that the sketchbook was created with pages
        try {
          const verifyResponse = await fetch(
            `/api/v1/sketchbook/get-by-id/${sketchbookId}`
          );
          const verifyData = await verifyResponse.json();

          console.log('Verification response:', verifyData);

          // Check if pages is null but activePage is set to '01'
          // This indicates the backend has created a page but returned null in the response
          if (
            verifyData.data.pages === null &&
            verifyData.data.activePage === '01'
          ) {
            console.log(
              'Sketchbook has activePage "01" but null pages - this is normal for new sketchbooks'
            );
            console.log(
              'The page will be initialized when the sketchbook is opened'
            );
          }
          // Check if pages array is empty
          else if (
            !verifyData.data.pages ||
            verifyData.data.pages.length === 0
          ) {
            console.warn(
              'Sketchbook created without pages, adding a default page'
            );

            // Add a default page if none exists
            const defaultPagePayload = {
              id: '01',
              name: '01',
              custom_chart_ids: [],
              ai_chart_ids: [],
              ai_image_ids: [],
              ai_summary_ids: [],
              layouts: [],
            };

            await addPageToSketchbook({
              payload: defaultPagePayload,
              id: sketchbookId,
            }).unwrap();
          } else {
            console.log(
              `Sketchbook has ${verifyData.data.pages.length} pages, no need to add more`
            );

            // Check for duplicate pages with the same ID
            const pageIds = new Set();
            const duplicatePages: any[] = [];

            verifyData.data.pages.forEach((page: any) => {
              if (pageIds.has(page.id)) {
                duplicatePages.push(page.id);
              } else {
                pageIds.add(page.id);
              }
            });

            if (duplicatePages.length > 0) {
              console.warn(
                `Found duplicate pages with IDs: ${duplicatePages.join(', ')}`
              );
            }
          }
        } catch (verifyError) {
          console.error('Error verifying sketchbook:', verifyError);
        }

        toast.dismiss(toastId);
        toast.success(`Created new ${template.name} sketchbook`);

        // Navigate to the sketchbook page with a small delay to allow the page to be added
        setTimeout(() => {
          // Use the correct navigation format that matches existing sketchbooks
          navigate('/sketchbook', { state: { sketchbookId } });

          // Close the template selector if provided
          if (onClose) {
            onClose();
          }
        }, 500);
      }
    } catch (error) {
      console.error('Error creating sketchbook from template:', error);
      toast.dismiss(toastId);
      toast.error('Failed to create sketchbook from template');
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2>Choose a Template</h2>
        <p>Select a template to create a new sketchbook</p>
      </div>

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          sx={{
            '& .MuiTab-root': {
              color: 'var(--color-text-secondary)',
              '&.Mui-selected': {
                color: 'var(--color-primary)',
              },
            },
            '& .MuiTabs-indicator': {
              backgroundColor: 'var(--color-primary)',
            },
          }}
        >
          <Tab label="Built-in Templates" />
          <Tab label="My Templates" />
        </Tabs>
      </Box>

      {activeTab === 0 && (
        <div className={styles.templatesGrid}>
          {templates.map((template) => (
            <TemplateCard
              key={template.id}
              template={template}
              onClick={() => handleTemplateSelect(template)}
              isCreating={isCreating}
            />
          ))}
        </div>
      )}

      {activeTab === 1 && (
        <div>
          {isLoadingCustomTemplates ? (
            <div className={styles.loadingContainer}>
              <CircularProgress size={40} />
              <Typography variant="body1" sx={{ mt: 2 }}>
                Loading your templates...
              </Typography>
            </div>
          ) : customTemplates.length > 0 ? (
            <div className={styles.templatesGrid}>
              {customTemplates.map((template) => (
                <CustomTemplateCard
                  key={template.id}
                  template={template}
                  onClick={() => handleCustomTemplateSelect(template)}
                  onDelete={handleDeleteConfirm}
                  isCreating={isCreating}
                  isDeleting={deletingTemplateId === template.id}
                />
              ))}
            </div>
          ) : (
            <div className={styles.emptyState}>
              <Typography variant="h6" sx={{ mb: 1 }}>
                No custom templates yet
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Save your sketchbooks as templates to reuse them later.
              </Typography>
            </div>
          )}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this template? This action cannot be
            undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDeleteConfirm(false)}>Cancel</Button>
          <Button
            onClick={handleDeleteTemplate}
            color="error"
            variant="contained"
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default SketchbookTemplates;
