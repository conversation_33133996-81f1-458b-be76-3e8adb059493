/**
 * Test utility for notification API integration
 * This can be called from browser console to test the mark-as-read functionality
 */

export const testNotificationMarkAsRead = async () => {
  try {
    console.log('🧪 Testing Notification Mark-as-Read API Integration...');

    // Get current user ID
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const userId = user.id;

    if (!userId) {
      console.error('❌ No user ID found in localStorage');
      return;
    }

    console.log('👤 Current User ID:', userId);

    // Get auth token
    const token = localStorage.getItem('token')?.replace(/"/g, '');
    if (!token) {
      console.error('❌ No auth token found');
      return;
    }

    // Step 1: Fetch notifications
    console.log('\n📥 Step 1: Fetching notifications...');
    const fetchResponse = await fetch(
      `/api/v1/notifications/get-all/${userId}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );

    if (!fetchResponse.ok) {
      console.error('❌ Failed to fetch notifications:', fetchResponse.status);
      return;
    }

    const fetchData = await fetchResponse.json();
    console.log('✅ Notifications fetched:', fetchData);

    if (!fetchData.data || fetchData.data.length === 0) {
      console.log('ℹ️ No notifications found to test with');
      return;
    }

    // Step 2: Find an unread notification
    const unreadNotification = fetchData.data.find((notification: any) => {
      const userRecipient = notification.recipients.find(
        (r: any) => r.userId === userId
      );
      return userRecipient && !userRecipient.readAt;
    });

    if (!unreadNotification) {
      console.log(
        'ℹ️ No unread notifications found. Using first notification for test.'
      );
      const firstNotification = fetchData.data[0];

      // Step 3: Test mark as read API
      console.log('\n✅ Step 2: Testing mark-as-read API...');
      console.log('📝 Notification ID:', firstNotification.id);

      const markReadResponse = await fetch(
        `/api/v1/notifications/${firstNotification.id}/mark-as-read/${userId}`,
        {
          method: 'PUT',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (markReadResponse.ok) {
        const markReadData = await markReadResponse.json();
        console.log('✅ Mark as read successful:', markReadData);
      } else {
        console.error(
          '❌ Mark as read failed:',
          markReadResponse.status,
          markReadResponse.statusText
        );
      }

      return;
    }

    // Step 3: Test mark as read API with unread notification
    console.log(
      '\n✅ Step 2: Testing mark-as-read API with unread notification...'
    );
    console.log('📝 Unread Notification ID:', unreadNotification.id);

    const markReadResponse = await fetch(
      `/api/v1/notifications/${unreadNotification.id}/mark-as-read/${userId}`,
      {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );

    if (markReadResponse.ok) {
      const markReadData = await markReadResponse.json();
      console.log('✅ Mark as read successful:', markReadData);

      // Step 4: Verify the notification is now marked as read
      console.log('\n🔍 Step 3: Verifying notification is marked as read...');
      const verifyResponse = await fetch(
        `/api/v1/notifications/get-all/${userId}`,
        {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (verifyResponse.ok) {
        const verifyData = await verifyResponse.json();
        const updatedNotification = verifyData.data.find(
          (n: any) => n.id === unreadNotification.id
        );

        if (updatedNotification) {
          const userRecipient = updatedNotification.recipients.find(
            (r: any) => r.userId === userId
          );
          if (userRecipient?.readAt) {
            console.log(
              '✅ Verification successful: Notification is now marked as read'
            );
            console.log('📅 Read at:', userRecipient.readAt);
          } else {
            console.log('⚠️ Verification failed: Notification is still unread');
          }
        }
      }
    } else {
      console.error(
        '❌ Mark as read failed:',
        markReadResponse.status,
        markReadResponse.statusText
      );
    }

    console.log('\n🎉 Test completed!');
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
};

/**
 * Test the new notification targeting functionality
 */
export const testNotificationTargeting = async () => {
  try {
    console.log('🧪 Testing Notification Targeting API Integration...');

    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const userId = user.id;

    if (!userId) {
      console.error('❌ No user ID found in localStorage');
      return;
    }

    const token = localStorage.getItem('token')?.replace(/"/g, '');
    if (!token) {
      console.error('❌ No auth token found');
      return;
    }

    const testWorkflowId = 'test-workflow-123';

    // Test 1: Create notification for participants
    console.log('\n📝 Test 1: Creating notification for participants...');
    const participantsResponse = await fetch(
      '/api/v1/notifications/create?notify=participants',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          workflowId: testWorkflowId,
          title: 'Test Participants Notification',
          content: 'This notification should go to participants only',
        }),
      }
    );

    if (participantsResponse.ok) {
      const data = await participantsResponse.json();
      console.log('✅ Participants notification created:', data);
    } else {
      console.error(
        '❌ Failed to create participants notification:',
        participantsResponse.status
      );
    }

    // Test 2: Create notification for creator
    console.log('\n📝 Test 2: Creating notification for creator...');
    const creatorResponse = await fetch(
      '/api/v1/notifications/create?notify=creator',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          workflowId: testWorkflowId,
          title: 'Test Creator Notification',
          content: 'This notification should go to creator only',
        }),
      }
    );

    if (creatorResponse.ok) {
      const data = await creatorResponse.json();
      console.log('✅ Creator notification created:', data);
    } else {
      console.error(
        '❌ Failed to create creator notification:',
        creatorResponse.status
      );
    }

    // Test 3: Create notification for both
    console.log('\n📝 Test 3: Creating notification for both...');
    const bothResponse = await fetch(
      '/api/v1/notifications/create?notify=both',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          workflowId: testWorkflowId,
          title: 'Test Both Notification',
          content:
            'This notification should go to both creator and participants',
        }),
      }
    );

    if (bothResponse.ok) {
      const data = await bothResponse.json();
      console.log('✅ Both notification created:', data);
    } else {
      console.error(
        '❌ Failed to create both notification:',
        bothResponse.status
      );
    }

    console.log('\n🎉 Notification targeting tests completed!');
  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
};

// Make it available globally for console testing
(window as any).testNotificationMarkAsRead = testNotificationMarkAsRead;
(window as any).testNotificationTargeting = testNotificationTargeting;

console.log('🧪 Notification API test utilities loaded:');
console.log(
  '- Run testNotificationMarkAsRead() to test mark-as-read functionality'
);
console.log(
  '- Run testNotificationTargeting() to test new targeting functionality'
);
