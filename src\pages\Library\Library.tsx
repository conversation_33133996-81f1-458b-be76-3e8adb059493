import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Grid from '@mui/material/Grid';
import Card from '../../components/common/card/Card';
import ProjectDialog from '../../components/common/ProjectDialog/ProjectDialog';
import { predefinedFiles } from '../../utils/predefinedFiles';
import { PredefinedFile } from '../../types/predefinedFiles';
import { PredefinedPrompt } from '../../types/predefinedPrompts';
import styles from './Library.module.css';
import { useUploadFileMutation } from '../../services/chatServices';
import { getSimplifiedFileExtension } from '../../utils/getFileExtention';
import { createFormData } from '../../utils/formDataHelper';
import useLocalStorage from '../../hooks/useLocalStorage';
import toast from 'react-hot-toast';

// Dummy users for demonstration
const users = [
  { name: 'AI Model', avatar: 'https://i.pravatar.cc/150?img=1' },
  { name: 'System', avatar: 'https://i.pravatar.cc/150?img=2' },
];

const Library = () => {
  const navigate = useNavigate();
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedPredefinedFile, setSelectedPredefinedFile] =
    useState<PredefinedFile | null>(null);
  const [selectedPrompt, setSelectedPrompt] = useState<PredefinedPrompt | null>(
    null
  );
  const [title, setTitle] = useState('');
  const [rangeInput, setRangeInput] = useState('');
  const [uploadFile] = useUploadFileMutation();
  const [localstorage] = useLocalStorage('user', []);
  const [loading, setLoading] = useState(false);

  // Create a mock File object from predefined file data
  const createMockFile = (predefinedFile: PredefinedFile): File => {
    const blob = new Blob(['Mock file content for ' + predefinedFile.name], {
      type: predefinedFile.mimeType,
    });
    const file = new File([blob], predefinedFile.name, {
      type: predefinedFile.mimeType,
      lastModified: Date.now(),
    });
    return file;
  };

  const handleCardClick = (predefinedFile: PredefinedFile) => {
    setSelectedPredefinedFile(predefinedFile);
    setTitle(predefinedFile.name.replace(/\.[^/.]+$/, '')); // Remove file extension
    setOpenDialog(true);
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
    setSelectedPredefinedFile(null);
    setSelectedPrompt(null);
    setTitle('');
    setRangeInput('');
  };

  const handleProjectSelection = async (
    projectId: string,
    pageRanges: string[],
    promptText: string,
    _file: File,
    _promptId: string
  ) => {
    try {
      if (!selectedPredefinedFile) {
        throw new Error('No predefined file selected');
      }

      if (!projectId) {
        throw new Error('Project ID is required');
      }

      if (!localstorage?.[0]?.id) {
        throw new Error('User ID is required');
      }

      setLoading(true);

      // For predefined files, we simulate the upload process
      // In a real implementation, you might want to fetch the actual file content
      // or use a different API endpoint for predefined files

      const mockFile = createMockFile(selectedPredefinedFile);
      const numberOfPages = selectedPredefinedFile.pageCount || 1;
      const fileExtension = getSimplifiedFileExtension(mockFile);

      if (!fileExtension) {
        throw new Error('Invalid file type');
      }

      // Create form data similar to regular file upload
      const formData = createFormData({
        userId: localstorage[0].id,
        projectId,
        pageNumber: rangeInput || '1',
        fileName: mockFile.name,
        fileType: fileExtension,
        title,
        prompt: promptText || ' ',
        file: mockFile,
        response_type: 'summary',
      });

      // For predefined files, you might want to add additional metadata
      // to indicate this is a predefined file with existing context
      formData.append('isPredefinedFile', 'true');
      formData.append('predefinedFileId', selectedPredefinedFile.id);

      const result = await uploadFile({
        formData,
        onProgress: (
          _progress: number,
          _status: 'uploading' | 'processing'
        ) => {
          // Handle progress if needed
        },
      }).unwrap();

      const uploadResponse = result as any;

      toast.success('Predefined file loaded successfully!', { duration: 6000 });

      // Navigate to chat with the predefined file context
      navigate('/chat', {
        state: {
          summary: uploadResponse.summary || selectedPredefinedFile.description,
          tables: uploadResponse.tables || [],
          questions:
            uploadResponse.suggested_questions ||
            selectedPredefinedFile.suggestedQuestions,
          file: mockFile,
          numberOfPages,
          insightProjectId: uploadResponse.object_id,
          projectId,
          pageRange: pageRanges,
          uploadFileTitle: title,
          fileUpload: true,
          isPredefinedFile: true,
          predefinedFileData: selectedPredefinedFile,
        },
      });

      handleDialogClose();
    } catch (error) {
      console.error('Predefined file loading failed:', error);
      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to load predefined file',
        { duration: 6000 }
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.libraryContainer}>
      <div className={styles.header}>
        <h1 className={styles.title}>Document Library</h1>
        {/* <p className={styles.subtitle}>
          Select from our pre-analyzed documents to save processing time and
          costs. These files have already been processed by our AI model.
        </p> */}
      </div>

      <div className={styles.content}>
        <Grid container spacing={3}>
          {predefinedFiles.map((file: PredefinedFile) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={file.id}>
              <Card
                title={file.name}
                users={users}
                timeAgo={file.lastModified}
                status="Published"
                role="creator"
                initials={file.name.slice(0, 2).toUpperCase()}
                handleCardClick={() => handleCardClick(file)}
                fromProject={false}
              />
            </Grid>
          ))}
        </Grid>
      </div>

      {openDialog && selectedPredefinedFile && (
        <ProjectDialog
          onClose={handleDialogClose}
          onSelectProject={handleProjectSelection}
          rangeInput={rangeInput}
          setRangeInput={setRangeInput}
          title={title}
          setTitle={setTitle}
          selectedPrompt={selectedPrompt}
          uploadedFile={createMockFile(selectedPredefinedFile)}
        />
      )}
    </div>
  );
};

export default Library;
