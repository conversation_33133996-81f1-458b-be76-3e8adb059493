import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import {
  WorkflowNotification,
  NotificationState,
} from '../../types/workflow/index';

const initialState: NotificationState = {
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  lastFetched: null,
  error: null,
  isCreating: false,
  isMarkingAsRead: false,
};

const notificationSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    setCreating: (state, action: PayloadAction<boolean>) => {
      state.isCreating = action.payload;
    },

    setMarkingAsRead: (state, action: PayloadAction<boolean>) => {
      state.isMarkingAsRead = action.payload;
    },

    setNotifications: (
      state,
      action: PayloadAction<WorkflowNotification[]>
    ) => {
      state.notifications = action.payload;
      state.unreadCount = action.payload.filter((n) => !n.read).length;
      state.lastFetched = new Date().toISOString();
    },

    addNotification: (state, action: PayloadAction<WorkflowNotification>) => {
      // Check if notification already exists to prevent duplicates
      const existingIndex = state.notifications.findIndex(
        (n) => n.id === action.payload.id
      );

      if (existingIndex === -1) {
        state.notifications.unshift(action.payload);
        if (!action.payload.read) {
          state.unreadCount += 1;
        }
      }
    },

    markAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(
        (n) => n.id === action.payload
      );
      if (notification && !notification.read) {
        notification.read = true;
        notification.readAt = new Date().toISOString();
        state.unreadCount = Math.max(0, state.unreadCount - 1);
      }
    },

    markAllAsRead: (state) => {
      const now = new Date().toISOString();
      state.notifications.forEach((notification) => {
        if (!notification.read) {
          notification.read = true;
          notification.readAt = now;
        }
      });
      state.unreadCount = 0;
    },

    removeNotification: (state, action: PayloadAction<string>) => {
      const index = state.notifications.findIndex(
        (n) => n.id === action.payload
      );
      if (index !== -1) {
        const notification = state.notifications[index];
        if (!notification.read) {
          state.unreadCount = Math.max(0, state.unreadCount - 1);
        }
        state.notifications.splice(index, 1);
      }
    },

    clearOldNotifications: (state, action: PayloadAction<number>) => {
      const daysToKeep = action.payload;
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const filteredNotifications = state.notifications.filter(
        (notification) => {
          const notificationDate = new Date(notification.createdAt);
          return notificationDate > cutoffDate;
        }
      );

      state.notifications = filteredNotifications;
      state.unreadCount = filteredNotifications.filter((n) => !n.read).length;
    },

    updateNotificationStatus: (
      state,
      action: PayloadAction<{
        workflowId: string;
        status: 'approved' | 'rejected' | 'conditional' | 'in-progress';
      }>
    ) => {
      const { workflowId, status } = action.payload;
      state.notifications.forEach((notification) => {
        if (notification.workflowId === workflowId) {
          notification.status = status;
          // Update action required based on status
          if (status === 'approved' || status === 'rejected') {
            notification.actionRequired = false;
          }
        }
      });
    },
  },
});

export const {
  setLoading,
  setError,
  setCreating,
  setMarkingAsRead,
  setNotifications,
  addNotification,
  markAsRead,
  markAllAsRead,
  removeNotification,
  clearOldNotifications,
  updateNotificationStatus,
} = notificationSlice.actions;

export default notificationSlice.reducer;

// Selectors
export const selectNotifications = (state: {
  notifications: NotificationState;
}) => state.notifications.notifications;

export const selectUnreadCount = (state: {
  notifications: NotificationState;
}) => state.notifications.unreadCount;

export const selectUnreadNotifications = (state: {
  notifications: NotificationState;
}) => state.notifications.notifications.filter((n) => !n.read);

export const selectActionRequiredNotifications = (state: {
  notifications: NotificationState;
}) =>
  state.notifications.notifications.filter((n) => n.actionRequired && !n.read);

export const selectNotificationsByWorkflow =
  (workflowId: string) => (state: { notifications: NotificationState }) =>
    state.notifications.notifications.filter(
      (n) => n.workflowId === workflowId
    );

export const selectIsLoading = (state: { notifications: NotificationState }) =>
  state.notifications.isLoading;
