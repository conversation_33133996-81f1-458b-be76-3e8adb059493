import React, {
  ReactNode,
  useState,
  useEffect,
  useRef,
  useCallback,
} from 'react';
import { Drawer, IconButton, Tooltip } from '@mui/material';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import DragHandleIcon from '@mui/icons-material/DragHandle';
import styles from './StyledDrawer.module.css';
import { ResizableBox } from 'react-resizable';
import 'react-resizable/css/styles.css';
import { useTabletDetection } from '../../../hooks/useTabletDetection';
import { triggerHapticFeedback } from '../../../utils/touchUtils';

interface StyledDrawerProps {
  open: boolean;
  onToggle: () => void;
  anchor: 'left' | 'right';
  children: ReactNode;
  minWidth?: number;
  maxWidth?: number;
}

export const StyledDrawer = ({
  open,
  onToggle,
  anchor,
  children,
  minWidth = 200,
  maxWidth = 400,
}: StyledDrawerProps) => {
  const [width, setWidth] = useState(240);
  const [isVisible, setIsVisible] = useState(open);
  const [isResizing, setIsResizing] = useState(false);
  const { isTablet, isTouchDevice } = useTabletDetection();

  // Touch resize state
  const touchResizeRef = useRef({
    isResizing: false,
    startX: 0,
    startWidth: 0,
  });

  const handleResizeStart = () => {
    setIsResizing(true);
    // Add a class to the body to prevent text selection during resize
    document.body.style.cursor = 'col-resize';
  };

  const handleResizeStop = () => {
    setIsResizing(false);
    // Reset cursor
    document.body.style.cursor = '';
  };

  const handleResize = (_: any, { size }: { size: { width: number } }) => {
    const newWidth = Math.min(Math.max(size.width, minWidth), maxWidth);

    // If width is less than 30% of minWidth, hide the drawer completely
    if (newWidth < minWidth * 0.3 && isVisible) {
      setIsVisible(false);
      onToggle(); // Call the toggle function to update parent component state
    } else if (newWidth >= minWidth * 0.3 && !isVisible) {
      setIsVisible(true);
    }

    setWidth(newWidth);
  };

  // Touch resize handlers
  const handleTouchStart = useCallback(
    (e: React.TouchEvent) => {
      if (!isTouchDevice) return;

      const touch = e.touches[0];
      touchResizeRef.current = {
        isResizing: true,
        startX: touch.clientX,
        startWidth: width,
      };

      setIsResizing(true);
      document.body.style.cursor = 'col-resize';

      if (isTouchDevice) {
        triggerHapticFeedback('light');
      }

      e.preventDefault();
    },
    [isTouchDevice, width]
  );

  const handleTouchMove = useCallback(
    (e: React.TouchEvent) => {
      if (!touchResizeRef.current.isResizing) return;

      const touch = e.touches[0];
      const deltaX = touch.clientX - touchResizeRef.current.startX;

      let newWidth;
      if (anchor === 'left') {
        newWidth = touchResizeRef.current.startWidth + deltaX;
      } else {
        newWidth = touchResizeRef.current.startWidth - deltaX;
      }

      // Apply smooth constraints
      newWidth = Math.min(Math.max(newWidth, minWidth * 0.2), maxWidth);

      // Smooth transition for hiding/showing
      if (newWidth < minWidth * 0.3) {
        if (isVisible) {
          setIsVisible(false);
          onToggle();
        }
        // Allow width to go smaller for smooth animation
        newWidth = Math.max(newWidth, 50);
      } else if (newWidth >= minWidth * 0.3 && !isVisible) {
        setIsVisible(true);
      }

      // Use requestAnimationFrame for smooth updates
      requestAnimationFrame(() => {
        setWidth(newWidth);
      });

      e.preventDefault();
      e.stopPropagation();
    },
    [anchor, minWidth, maxWidth, isVisible, onToggle]
  );

  const handleTouchEnd = useCallback(
    (e: React.TouchEvent) => {
      if (!touchResizeRef.current.isResizing) return;

      touchResizeRef.current.isResizing = false;
      setIsResizing(false);
      document.body.style.cursor = '';

      if (isTouchDevice) {
        triggerHapticFeedback('light');
      }

      e.preventDefault();
    },
    [isTouchDevice]
  );

  // Custom resize handle component with forwardRef
  const CustomResizeHandle = React.forwardRef(
    ({ handleAxis, ...props }: any, ref: React.Ref<HTMLDivElement>) => {
      // We can't use MUI Tooltip directly with the resize handle due to ref forwarding issues
      // Instead, we'll use a custom tooltip approach with CSS
      return (
        <div
          ref={ref}
          className={`${styles.resizeHandle} ${styles[`resizeHandle${handleAxis.toUpperCase()}`]} ${isTouchDevice ? styles.touchOptimized : ''}`}
          {...props}
          aria-label="Drag to resize drawer"
          data-tooltip={
            isTouchDevice ? 'Touch and drag to resize' : 'Drag to resize drawer'
          }
          onMouseDown={(e) => {
            // Prevent text selection when starting to drag
            e.preventDefault();
            // Call the original onMouseDown handler from props
            if (props.onMouseDown) {
              props.onMouseDown(e);
            }
          }}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          <div className={styles.resizeHandleInner}>
            <DragHandleIcon className={styles.dragIcon} />
          </div>
        </div>
      );
    }
  );

  // Effect to sync the open prop with isVisible state
  useEffect(() => {
    setIsVisible(open);
  }, [open]);

  return (
    <div className={styles.drawerContainer}>
      {!open && (
        <Tooltip
          title="Show drawer"
          placement={anchor === 'left' ? 'right' : 'left'}
          arrow
          enterDelay={500}
          leaveDelay={200}
        >
          <IconButton
            onClick={onToggle}
            className={`${styles.drawerButton}`}
            data-position={anchor}
            aria-label="Show drawer"
            size="small"
          >
            {anchor === 'left' ? (
              <ChevronRightIcon fontSize="small" />
            ) : (
              <ChevronLeftIcon fontSize="small" />
            )}
          </IconButton>
        </Tooltip>
      )}
      {open && (
        <div className={styles.resizableContainer}>
          {isTouchDevice ? (
            // Custom touch-optimized drawer for tablets
            <div
              className={styles.resizableBox}
              style={{
                width: `${width}px`,
                height: window.innerHeight - 118,
                position: 'relative',
              }}
            >
              <CustomResizeHandle handleAxis={anchor === 'left' ? 'e' : 'w'} />
              <div className={styles.drawerHeader}>
                <div className={styles.drawerTitle}>
                  {anchor === 'left' ? 'Controls' : 'Properties'}
                </div>
                <Tooltip
                  title="Hide drawer"
                  placement={anchor === 'left' ? 'right' : 'left'}
                  arrow
                  enterDelay={500}
                  leaveDelay={200}
                >
                  <IconButton
                    onClick={onToggle}
                    className={styles.closeButton}
                    aria-label="Hide drawer"
                    size="small"
                  >
                    {anchor === 'left' ? (
                      <ChevronLeftIcon fontSize="small" />
                    ) : (
                      <ChevronRightIcon fontSize="small" />
                    )}
                  </IconButton>
                </Tooltip>
              </div>
              <div
                className={`${styles.drawerContent} ${isResizing ? styles.noSelect : ''}`}
              >
                {children}
              </div>
            </div>
          ) : (
            // Standard ResizableBox for desktop
            <ResizableBox
              width={width}
              height={window.innerHeight - 118}
              onResize={handleResize}
              onResizeStart={handleResizeStart}
              onResizeStop={handleResizeStop}
              resizeHandles={anchor === 'left' ? ['e'] : ['w']}
              handle={<CustomResizeHandle />}
              className={styles.resizableBox}
              draggableOpts={{
                enableUserSelectHack: true,
                // Prevent the default behavior
                preventDefault: true,
                // Prevent propagation to avoid text selection
                stopPropagation: true,
              }}
            >
              <div className={styles.drawerHeader}>
                <div className={styles.drawerTitle}>
                  {anchor === 'left' ? 'Controls' : 'Properties'}
                </div>
                <Tooltip
                  title="Hide drawer"
                  placement={anchor === 'left' ? 'right' : 'left'}
                  arrow
                  enterDelay={500}
                  leaveDelay={200}
                >
                  <IconButton
                    onClick={onToggle}
                    className={styles.closeButton}
                    aria-label="Hide drawer"
                    size="small"
                  >
                    {anchor === 'left' ? (
                      <ChevronLeftIcon fontSize="small" />
                    ) : (
                      <ChevronRightIcon fontSize="small" />
                    )}
                  </IconButton>
                </Tooltip>
              </div>
              <div
                className={`${styles.drawerContent} ${isResizing ? styles.noSelect : ''}`}
              >
                {children}
              </div>
            </ResizableBox>
          )}
        </div>
      )}
    </div>
  );
};
