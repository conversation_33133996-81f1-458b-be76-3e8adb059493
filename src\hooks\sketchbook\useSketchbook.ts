import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import {
  setPages,
  setActivePage,
  updateCharts,
  updateLayout,
  updateFlowNodes,
  updateFlowEdges,
  setPageSize,
} from '../../store/sketchbookSlice';
import { useGetSketchbookByIdQuery } from '../../services/sketchbookServices';
import { transformChartDataForSketchbookArray } from '../../utils/transformChartForSketchbook';

export const useSketchbook = (sketchbookId: string) => {
  const dispatch = useDispatch();
  const [projectTitle, setProjectTitle] = useState('Untitled Sketchbook');
  const [projectId, setProjectId] = useState('undefined');
  const [mongoIdOfInsightsForComplinces, setMongoIdOfInsightsForComplinces] =
    useState('undefined');
  const [initialChartsLoaded, setInitialChartsLoaded] = useState(false);
  const [lastProcessedDataTimestamp, setLastProcessedDataTimestamp] =
    useState(0);

  const { data, refetch: fetchData } = useGetSketchbookByIdQuery(
    { sketchbookId },
    { skip: !sketchbookId }
  );

  useEffect(() => {
    if (sketchbookId) {
      fetchData();
    }
  }, [sketchbookId, fetchData]);

  useEffect(() => {
    if (data) {
      // Create a timestamp for this data update
      const currentTimestamp = Date.now();

      // If we've processed this data recently (within 1 second), skip processing
      // This prevents double updates when adding pages
      if (currentTimestamp - lastProcessedDataTimestamp < 1000) {
        console.log('Skipping data processing - too soon after last update');
        console.log(
          'Time since last update:',
          currentTimestamp - lastProcessedDataTimestamp,
          'ms'
        );
        return;
      }

      console.log(
        'Processing sketchbook data update at timestamp:',
        currentTimestamp
      );

      const {
        project_title,
        pages,
        activePage,
        pageSize,
        pageEnabled,
        flowNodes,
        flowEdges,
        id: projectId,
        project_id: mongoIdOfInsights,
        sketchbook_name,
      } = data?.data || {};

      setMongoIdOfInsightsForComplinces(mongoIdOfInsights || 'undefined');
      // Use sketchbook_name first, then project_title, then default
      setProjectTitle(
        sketchbook_name || project_title || 'Untitled Sketchbook'
      );
      setProjectId(projectId || 'undefined');
      dispatch(updateFlowNodes(flowNodes || []));
      dispatch(updateFlowEdges(flowEdges || []));

      // Set page size from backend data if available
      if (pageSize) {
        console.log('Setting page size from backend:', pageSize);
        dispatch(setPageSize(pageSize));
      }

      // Log if pages is null or empty
      if (!pages || pages.length === 0) {
        console.warn('Pages is null or empty in sketchbook data:', data);
      }

      // Only process charts if not already loaded or if we need to refresh
      if (!initialChartsLoaded) {
        // Check if pages is null or empty
        if (!pages || pages.length === 0) {
          console.log('No pages found in sketchbook data');

          // Check if this is a newly created sketchbook with activePage set
          // This indicates the backend has created a page but returned null in the response
          if (activePage === '01') {
            console.log(
              'Sketchbook has activePage "01" but null pages - initializing with default page'
            );
            // Initialize with a default page since we know the backend has created it
            dispatch(setPages([{ id: '01', name: '01' }]));
            dispatch(setActivePage('01'));
            dispatch(updateCharts({ pageId: '01', charts: [] }));
            dispatch(updateLayout({ pageId: '01', layout: [] }));
            setInitialChartsLoaded(true);
            return;
          }

          console.log('Letting SketchBookPage component handle initialization');
          // Don't initialize with a default page here - let the SketchBookPage component handle it
          // This prevents duplicate page creation
          setInitialChartsLoaded(true);
          return;
        } else {
          // Log pages from backend for debugging
          console.log(
            'Pages from backend:',
            pages.map((p: any) => ({ id: p.id, name: p.name }))
          );

          // Check for duplicate page IDs
          const pageIds = new Set<string>();
          const duplicatePageIds: string[] = [];

          pages.forEach((page: any) => {
            if (pageIds.has(page.id)) {
              duplicatePageIds.push(page.id);
            } else {
              pageIds.add(page.id);
            }
          });

          if (duplicatePageIds.length > 0) {
            console.warn('Duplicate page IDs detected:', duplicatePageIds);
          }

          // Use only unique pages to prevent duplicates
          const uniquePages = pages.filter(
            (page: any, index: number, self: any[]) =>
              index === self.findIndex((p: any) => p.id === page.id)
          );

          console.log(
            'Unique pages after filtering:',
            uniquePages.map((p: any) => ({ id: p.id, name: p.name }))
          );

          // Initialize Redux state for each page
          uniquePages.forEach((page: any) => {
            const elements = processPageElements(page);
            const transformedCharts =
              transformChartDataForSketchbookArray(elements);

            dispatch(
              updateCharts({
                pageId: page.id,
                charts: transformedCharts,
              })
            );

            dispatch(
              updateLayout({
                pageId: page.id,
                layout: page.layouts,
              })
            );
          });

          // Set active page, defaulting to the first page if the specified active page doesn't exist
          const activePageExists = uniquePages.some(
            (page: any) => page.id === activePage
          );
          const newActivePage = activePageExists
            ? activePage
            : uniquePages[0]?.id || '01';

          console.log('Setting active page to:', newActivePage);
          dispatch(setActivePage(newActivePage));

          // Update Redux store with unique pages
          dispatch(
            setPages(
              uniquePages.map((page: any) => ({
                id: page.id,
                name: page.name,
              }))
            )
          );
        }

        setInitialChartsLoaded(true);
      }

      // Update the timestamp of the last processed data
      setLastProcessedDataTimestamp(currentTimestamp);
    }
  }, [data, dispatch, initialChartsLoaded, lastProcessedDataTimestamp]);

  return {
    projectTitle,
    projectId,
    mongoIdOfInsightsForComplinces,
    sketchbookData: data?.data,
    initialChartsLoaded,
  };
};

const processPageElements = (page: any) => {
  const images = processImages(page.ai_images);
  const charts = processCharts(page.ai_charts);
  const customCharts = processCustomCharts(page.customCharts);
  // const texts = processTexts(page.ai_summary);
  const customTexts = processCustomTexts(page.customCharts?.textareaCharts);
  return [...charts, ...images, ...customCharts, ...customTexts];
};

// Helper functions for processing different chart types
const processImages = (aiImages: any[] = []) =>
  aiImages.map((image) => ({
    chart_id: image.imageId,
    type_of_chart: 'image',
    data: {
      src: image?.image,
      alt: 'AI Generated Image',
    },
    chartType: 'ai-image',
  }));

const processCharts = (aiCharts: any[] = []) =>
  aiCharts?.map((chart) => ({
    ...chart,
    chartType: 'ai-chart',
  }));

const processCustomCharts = (customCharts: any = {}) => {
  const genericCharts = (customCharts?.genericCharts || []).map(
    (chart: any) => ({
      chart_id: chart.id,
      chart_name: chart?.datasets[0]?.label,
      values: chart?.datasets[0]?.data,
      backgroundColor: chart?.datasets[0]?.backgroundColor,
      borderColor: chart?.datasets[0]?.borderColor,
      borderWidth: chart?.datasets[0]?.borderWidth,
      fill: chart?.datasets[0]?.fill,
      x_labels: chart?.labels,
      type_of_chart: chart?.type,
      options: chart?.options,
      position: { x: 0, y: 0 },
      size: { width: 400, height: 300 },
      chartType: 'custom-chart',
    })
  );

  // Process horizontal bar charts
  const horizontalbarCharts = (customCharts?.horizontalbarCharts || []).map(
    (chart: any) => ({
      chart_id: chart.id,
      chart_name: chart?.title || chart?.datasets[0]?.label,
      values: chart?.datasets[0]?.data,
      backgroundColor: chart?.datasets[0]?.backgroundColor,
      borderColor: chart?.datasets[0]?.borderColor,
      borderWidth: chart?.datasets[0]?.borderWidth,
      fill: chart?.datasets[0]?.fill,
      x_labels: chart?.labels,
      type_of_chart: chart?.type,
      options: chart?.options,
      position: { x: 0, y: 0 },
      size: { width: 400, height: 300 },
      chartType: 'custom-chart',
    })
  );

  // Process polar area charts
  const polarAreaCharts = (customCharts?.polarareaCharts || []).map(
    (chart: any) => ({
      chart_id: chart.id,
      chart_name: chart?.title || chart?.datasets[0]?.label,
      values: chart?.datasets[0]?.data,
      backgroundColor: chart?.datasets[0]?.backgroundColor,
      borderColor: chart?.datasets[0]?.borderColor,
      borderWidth: chart?.datasets[0]?.borderWidth,
      fill: chart?.datasets[0]?.fill,
      x_labels: chart?.labels,
      type_of_chart: chart?.type,
      options: chart?.options,
      position: { x: 0, y: 0 },
      size: { width: 400, height: 300 },
      chartType: 'custom-chart',
    })
  );

  // Process bubble charts
  const bubbleCharts = (customCharts?.bubbleCharts || []).map((chart: any) => ({
    chart_id: chart.id,
    chart_name: chart?.title || chart?.datasets[0]?.label,
    values: chart?.datasets[0]?.data,
    backgroundColor: chart?.datasets[0]?.backgroundColor,
    borderColor: chart?.datasets[0]?.borderColor,
    borderWidth: chart?.datasets[0]?.borderWidth,
    fill: chart?.datasets[0]?.fill,
    x_labels: chart?.labels,
    type_of_chart: chart?.type,
    options: chart?.options,
    position: { x: 0, y: 0 },
    size: { width: 400, height: 300 },
    chartType: 'custom-chart',
  }));

  // Process scatter charts
  const scatterCharts = (customCharts?.scatterCharts || []).map(
    (chart: any) => ({
      chart_id: chart.id,
      chart_name: chart?.title || chart?.datasets[0]?.label,
      values: chart?.datasets[0]?.data,
      backgroundColor: chart?.datasets[0]?.backgroundColor,
      borderColor: chart?.datasets[0]?.borderColor,
      borderWidth: chart?.datasets[0]?.borderWidth,
      fill: chart?.datasets[0]?.fill,
      x_labels: chart?.labels,
      type_of_chart: chart?.type,
      options: chart?.options,
      position: { x: 0, y: 0 },
      size: { width: 400, height: 300 },
      chartType: 'custom-chart',
    })
  );

  // Process burndown and timeline charts
  const burndownTimelineCharts = (
    customCharts?.burndownTimelineCharts || []
  ).map((chart: any) => ({
    chart_id: chart.id,
    chart_name: chart?.title || chart?.datasets[0]?.label,
    type_of_chart: chart?.type,
    data: {
      labels: chart?.labels,
      datasets: chart?.datasets,
    },
    options: chart?.options,
    position: { x: 0, y: 0 },
    size: { width: 400, height: 300 },
    chartType: 'custom-chart',
  }));

  // Process gauge charts
  const gaugeCharts = (customCharts?.gaugeCharts || []).map((chart: any) => ({
    chart_id: chart.id,
    chart_name: chart?.title,
    type_of_chart: chart?.type,
    data: {
      percent: chart?.datasets?.data || 0.5,
      text: chart?.datasets?.text || '',
    },
    options: chart?.options,
    position: { x: 0, y: 0 },
    size: { width: 400, height: 300 },
    chartType: 'custom-chart',
  }));

  // Process gantt charts
  const ganttCharts = (customCharts?.ganttCharts || []).map((chart: any) => ({
    chart_id: chart.id,
    chart_name: chart?.title,
    type_of_chart: chart?.type,
    data: {
      tasks: chart?.tasks || [],
    },
    options: chart?.options,
    position: { x: 0, y: 0 },
    size: { width: 800, height: 400 },
    chartType: 'custom-chart',
  }));

  return [
    ...genericCharts,
    ...horizontalbarCharts,
    ...polarAreaCharts,
    ...bubbleCharts,
    ...scatterCharts,
    ...burndownTimelineCharts,
    // ...timelineCharts,
    // ...burndownCharts,
    ...gaugeCharts,
    ...ganttCharts,
  ];
};

// const processTexts = (aiSummary: any[] = []) =>
//   aiSummary.map((text) => ({
//     chart_id: text.response_id,
//     type_of_chart: 'textarea',
//     data: {
//       markdown: text.summary,
//       style: {
//         fontSize: '14px',
//         lineHeight: '1.5',
//         padding: '12px',
//       },
//     },
//     position: { x: 0, y: 0 },
//     size: { width: 400, height: 300 },
//     chartType: 'ai-summary',
//   }));

const processCustomTexts = (textareaCharts: any[] = []) =>
  textareaCharts.map((text) => ({
    chart_id: text.id,
    type_of_chart: 'textarea',
    data: {
      markdown: text.data.markdown || '',
    },
    chartType: 'custom-chart',
  }));
