/**
 * Utility functions for markdown and LaTeX processing
 */

// Greek letters mapping for auto-conversion
export const GREEK_LETTERS = {
  Alpha: 'alpha',
  Beta: 'beta',
  Gamma: 'gamma',
  Delta: 'delta',
  Epsilon: 'epsilon',
  Zeta: 'zeta',
  Eta: 'eta',
  Theta: 'theta',
  Iota: 'iota',
  Kappa: 'kappa',
  Lambda: 'lambda',
  Mu: 'mu',
  Nu: 'nu',
  Xi: 'xi',
  Omicron: 'omicron',
  Pi: 'pi',
  Rho: 'rho',
  Sigma: 'sigma',
  Tau: 'tau',
  Upsilon: 'upsilon',
  Phi: 'phi',
  Chi: 'chi',
  Psi: 'psi',
  Omega: 'omega',
} as const;

// Common programming languages for syntax highlighting
export const PROGRAMMING_LANGUAGES = [
  'javascript',
  'typescript',
  'python',
  'java',
  'cpp',
  'c',
  'csharp',
  'php',
  'ruby',
  'go',
  'rust',
  'swift',
  'kotlin',
  'scala',
  'r',
  'sql',
  'html',
  'css',
  'scss',
  'sass',
  'json',
  'xml',
  'yaml',
  'markdown',
  'bash',
  'shell',
  'powershell',
  'dockerfile',
  'nginx',
] as const;

/**
 * Preprocesses malformed LaTeX patterns
 */
export function preprocessLatex(content: string): string {
  return (
    content
      // Fix \\alpha$$ -> $\alpha$
      .replace(/\\\\([a-zA-Z]+)\$\$/g, '$\\$1$')
      // Fix $\$\beta -> $\beta$
      .replace(/\$\\\$\\([a-zA-Z]+)/g, '$\\$1$')
      // Fix triple or more dollar signs
      .replace(/\$\$\$+/g, '$$')
      // Fix malformed display math delimiters
      .replace(/\$\$\$([^$]+)\$\$\$/g, '$$$$1$$')
      // Clean up standalone backslashes (but preserve LaTeX commands)
      .replace(/\\(?![a-zA-Z$()[\]{}])/g, '')
      // Fix common LaTeX delimiter issues
      .replace(/\\\[([\s\S]*?)\\\]/g, '$$$$1$$') // Display math \[...\] -> $$...$$
      .replace(/\\\(([\s\S]*?)\\\)/g, '$$1$')
  ); // Inline math \(...\) -> $...$
}

/**
 * Converts Greek letter names to LaTeX symbols
 */
export function convertGreekLetters(content: string): string {
  let processedContent = content;

  Object.entries(GREEK_LETTERS).forEach(([name, latex]) => {
    // Only replace if it's a standalone word and not already in LaTeX context
    const regex = new RegExp(`\\b${name}\\b(?![^$]*\\$)`, 'g');
    processedContent = processedContent.replace(regex, `$\\${latex}$`);

    // Also handle lowercase
    const lowerRegex = new RegExp(
      `\\b${name.toLowerCase()}\\b(?![^$]*\\$)`,
      'g'
    );
    processedContent = processedContent.replace(lowerRegex, `$\\${latex}$`);
  });

  return processedContent;
}

/**
 * Protects currency amounts from being interpreted as LaTeX
 */
export function protectCurrency(content: string): string {
  return (
    content
      // Protect simple currency amounts like $20, $100.50
      .replace(/\$(\d+(?:\.\d{1,2})?)\b/g, '\\$$$1')
      // Protect currency with commas like $1,000.50
      .replace(/\$(\d{1,3}(?:,\d{3})*(?:\.\d{1,2})?)\b/g, '\\$$$1')
      // Protect currency ranges like $10-$20
      .replace(/\$(\d+(?:\.\d{1,2})?)-\$(\d+(?:\.\d{1,2})?)/g, '\\$$$1-\\$$$2')
  );
}

/**
 * Rewrites relative links to use a base URL
 */
export function rewriteRelativeLinks(content: string, baseUrl: string): string {
  if (!baseUrl) return content;

  return content.replace(/\]\((\/[^\)]+)\)/g, `](${baseUrl}$1)`);
}

/**
 * Detects programming language from code block content
 */
export function detectLanguage(code: string): string {
  const trimmedCode = code.trim().toLowerCase();

  // JavaScript/TypeScript patterns
  if (
    trimmedCode.includes('function') ||
    trimmedCode.includes('const ') ||
    trimmedCode.includes('let ') ||
    trimmedCode.includes('var ') ||
    trimmedCode.includes('=>') ||
    trimmedCode.includes('import ')
  ) {
    if (
      trimmedCode.includes('interface ') ||
      trimmedCode.includes(': string') ||
      trimmedCode.includes(': number') ||
      trimmedCode.includes('type ')
    ) {
      return 'typescript';
    }
    return 'javascript';
  }

  // Python patterns
  if (
    trimmedCode.includes('def ') ||
    trimmedCode.includes('import ') ||
    trimmedCode.includes('from ') ||
    trimmedCode.includes('print(') ||
    trimmedCode.includes('if __name__')
  ) {
    return 'python';
  }

  // Java patterns
  if (
    trimmedCode.includes('public class') ||
    trimmedCode.includes('public static void') ||
    trimmedCode.includes('import java.') ||
    trimmedCode.includes('System.out.println')
  ) {
    return 'java';
  }

  // C/C++ patterns
  if (
    trimmedCode.includes('#include') ||
    trimmedCode.includes('int main(') ||
    trimmedCode.includes('printf(') ||
    trimmedCode.includes('cout <<')
  ) {
    return trimmedCode.includes('cout') || trimmedCode.includes('std::')
      ? 'cpp'
      : 'c';
  }

  // HTML patterns
  if (
    trimmedCode.includes('<html') ||
    trimmedCode.includes('<!doctype') ||
    trimmedCode.includes('<div') ||
    trimmedCode.includes('<span')
  ) {
    return 'html';
  }

  // CSS patterns
  if (
    trimmedCode.includes('{') &&
    trimmedCode.includes('}') &&
    (trimmedCode.includes(':') ||
      trimmedCode.includes('px') ||
      trimmedCode.includes('color') ||
      trimmedCode.includes('margin'))
  ) {
    return 'css';
  }

  // JSON patterns
  if (
    (trimmedCode.startsWith('{') && trimmedCode.endsWith('}')) ||
    (trimmedCode.startsWith('[') && trimmedCode.endsWith(']'))
  ) {
    try {
      JSON.parse(code);
      return 'json';
    } catch {
      // Not valid JSON, continue checking
    }
  }

  // SQL patterns
  if (
    trimmedCode.includes('select ') ||
    trimmedCode.includes('insert ') ||
    trimmedCode.includes('update ') ||
    trimmedCode.includes('delete ') ||
    trimmedCode.includes('create table')
  ) {
    return 'sql';
  }

  // Shell/Bash patterns
  if (
    trimmedCode.includes('#!/bin/bash') ||
    trimmedCode.includes('echo ') ||
    trimmedCode.includes('cd ') ||
    trimmedCode.includes('ls ') ||
    trimmedCode.includes('grep ')
  ) {
    return 'bash';
  }

  return 'text'; // Default fallback
}

/**
 * Comprehensive content preprocessing pipeline
 */
export function preprocessMarkdownContent(
  content: string,
  options: {
    baseUrl?: string;
    enableGreekConversion?: boolean;
    enableCurrencyProtection?: boolean;
    enableLatexPreprocessing?: boolean;
  } = {}
): string {
  const {
    baseUrl = '',
    enableGreekConversion = true,
    enableCurrencyProtection = true,
    enableLatexPreprocessing = true,
  } = options;

  let processedContent = content;
  console.log('preprocessMarkdownContent - original:', content);

  // Step 1: Preprocess LaTeX
  if (enableLatexPreprocessing) {
    processedContent = preprocessLatex(processedContent);
    console.log(
      'preprocessMarkdownContent - after LaTeX preprocessing:',
      processedContent
    );
  }

  // Step 2: Convert Greek letters
  if (enableGreekConversion) {
    processedContent = convertGreekLetters(processedContent);
    console.log(
      'preprocessMarkdownContent - after Greek conversion:',
      processedContent
    );
  }

  // Step 3: Protect currency
  if (enableCurrencyProtection) {
    processedContent = protectCurrency(processedContent);
    console.log(
      'preprocessMarkdownContent - after currency protection:',
      processedContent
    );
  }

  // Step 4: Rewrite relative links
  if (baseUrl) {
    processedContent = rewriteRelativeLinks(processedContent, baseUrl);
    console.log(
      'preprocessMarkdownContent - after link rewriting:',
      processedContent
    );
  }

  console.log('preprocessMarkdownContent - final result:', processedContent);
  return processedContent;
}

/**
 * Creates fallback content styles for error handling
 */
export function getFallbackStyles() {
  return {
    wrapper: {
      className: 'markdown-wrapper',
    },
    errorMessage: {
      color: '#d32f2f',
      fontSize: '12px',
      marginBottom: '8px',
      padding: '8px',
      backgroundColor: '#ffebee',
      border: '1px solid #ffcdd2',
      borderRadius: '4px',
    },
    content: {
      whiteSpace: 'pre-wrap' as const,
      fontFamily: 'monospace',
      padding: '8px',
      backgroundColor: '#f5f5f5',
      border: '1px solid #ddd',
      borderRadius: '4px',
    },
    errorDetails: {
      marginTop: '4px',
      fontSize: '11px',
    },
    errorPre: {
      margin: '4px 0',
      fontSize: '10px',
    },
  };
}
