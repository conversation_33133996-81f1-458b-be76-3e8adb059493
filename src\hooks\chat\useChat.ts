import { useState, useEffect } from 'react';
import { formatTimestamp } from '../../utils/timesAgo';
import { transformChartData } from '../../utils/chartMapper';
import { AI_BASE_URL } from '../../services/config';
import { createFormData } from '../../utils/formDataHelper';
import {
  getFileIcon,
  getSimplifiedFileExtension,
} from '../../utils/getFileExtention';
import toast from 'react-hot-toast';

interface UseChatProps {
  userInLocalStorage: any;
  projectID: string;
  getFileName: string;
  getFileType: string;
  getProjectTitle: string;
  pageRangeChanges: string;
  newProfileImage: string;
  senderIcon: string;
  botIcon: string;
  onMongoProjectIdChange: (id: string) => void;
  projectData?: any;
  handleOnQuestionClick?: (question: string) => void;
}

export const useChat = ({
  userInLocalStorage,
  projectID,
  getFileName,
  getFileType,
  getProjectTitle,
  pageRangeChanges,
  newProfileImage,
  senderIcon,
  botIcon,
  onMongoProjectIdChange,
  projectData,
  handleOnQuestionClick,
}: UseChatProps) => {
  const [messages, setMessages] = useState<any[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [currentStreamingMessageId, setCurrentStreamingMessageId] = useState<
    string | number | null
  >('');
  const [loadingFinalResponse, setLoadingFinalResponse] = useState(false);
  const [codeInput, setCodeInput] = useState(false);
  const [longResponseTimer, setLongResponseTimer] = useState<number | null>(
    null
  );
  const [isLongResponse, setIsLongResponse] = useState(false);

  // Load previous chat messages when projectData changes
  useEffect(() => {
    if (projectData?.prompt && projectData?.response) {
      const newMessages: any[] = [];
      onMongoProjectIdChange(projectData.id);

      const combinedArray = projectData.prompt.map((p: any, index: number) => ({
        prompt: p,
        response: projectData.response[index] || {},
        totalPages: projectData?.pages?.[index] ?? 0,
        pages: projectData?.pages || [],
      }));

      combinedArray.forEach((item: any) => {
        // Handle file upload message if file_name exists
        if (
          projectData.file_details &&
          (item.prompt === '' || item.prompt === ' ')
        ) {
          let getIndexOfFile: number = parseInt(
            localStorage.getItem('findIndexOfRecentFile') || '0',
            10
          );
          getIndexOfFile =
            getIndexOfFile >= projectData.file_details.length
              ? 0
              : getIndexOfFile;

          if (
            projectData.file_details[getIndexOfFile]?.custom_prompt !== '' &&
            projectData.file_details[getIndexOfFile]?.custom_prompt_content !==
              ''
          ) {
            newMessages.push({
              sender: 'user',
              preDefinedPrompt:
                projectData.file_details[getIndexOfFile].file_name,
              content: projectData.file_details[getIndexOfFile]?.custom_prompt
                ? projectData.file_details[getIndexOfFile]
                    ?.custom_prompt_content
                : '',
              type: 'light',
              icon: getFileIcon(
                projectData.file_details[getIndexOfFile].file_name
              ),
              image: newProfileImage || senderIcon,
              timestamp: new Date().toLocaleTimeString(),
              noOfPages: projectData.pages
                ? projectData.pages[getIndexOfFile]
                : 0,
            });
          } else {
            newMessages.push(
              {
                sender: 'user',
                content: projectData.file_details[getIndexOfFile].file_name,
                type: 'light',
                icon: getFileIcon(
                  projectData.file_details[getIndexOfFile].file_name
                ),
                image: newProfileImage || senderIcon,
                timestamp: new Date().toLocaleTimeString(),
                noOfPages: projectData.pages
                  ? projectData.pages[getIndexOfFile]
                  : 0,
              }
              // {
              //   type: 'light',
              //   sender: 'bot',
              //   content:
              //     'Tu for uploading your document. What would you like to do next ?',
              //   image: botIcon,
              //   timestamp: new Date().toLocaleTimeString(),
              // }
            );
          }

          localStorage.setItem(
            'findIndexOfRecentFile',
            (getIndexOfFile + 1).toString()
          );
        }

        // Handle user prompt
        if (item.prompt && (item.prompt == '' || item.prompt !== ' ')) {
          newMessages.push({
            sender: 'user',
            content: item.prompt,
            type: 'light',
            image: newProfileImage || senderIcon,
            timestamp: formatTimestamp(item.response.response_time),
          });
        }

        // Handle summary
        if (
          (item.response.summary &&
            item.response.summary !==
              'Thank you for uploading your document. What would you like to do next?') ||
          item.response.summary !==
            'Thank you for uploading your document. What would you like to do next  ?'
        ) {
          newMessages.push({
            sender: 'bot',
            content: item.response.summary,
            type: 'light',
            image: botIcon,
            timestamp: formatTimestamp(item.response.response_time),
            responseId: item.response.response_id,
          });
        }

        // Handle tables
        if (item.response.tables && item.response.tables.length > 0) {
          const chartMessages = transformChartData(item.response.tables);
          newMessages.push(...chartMessages);
        }

        // Handle images
        if (item.response.images && item.response.images.length > 0) {
          const chartImages = item.response.images.map((image: string) => ({
            sender: 'bot',
            content: image,
            type: 'chartImage',
            timestamp: formatTimestamp(item.response.response_time),
          }));
          newMessages.push(...chartImages);
        }

        // Handle source type
        if (item.response.source_type && item.response.source_type !== 'file') {
          newMessages.push({
            sender: 'bot',
            content: `Source: ${item.response.source_type}`,
            type: 'hint',
            timestamp: formatTimestamp(item.response.response_time),
          });
        }

        // Handle referenced Page
        if (item.response.source_type === 'file' && item.response.page_no) {
          newMessages.push({
            sender: 'bot',
            content: `Source: ${item.response.source_type} | Referenced Pages ${item.response.page_no}`,
            type: 'hint',
            timestamp: formatTimestamp(item.response.response_time),
          });
        }

        // Handle suggested questions
        if (
          item.response.suggested_questions &&
          item.response.suggested_questions.length > 0
        ) {
          newMessages.push({
            type: 'hint',
            sender: 'bot',
            content: 'Suggested follow up questions:',
            timestamp: formatTimestamp(item.response.response_time),
          });

          const questionMessages = item.response.suggested_questions.map(
            (question: string) => ({
              handleOnClick: () => handleOnQuestionClick?.(question),
              type: 'dark',
              sender: 'bot',
              content: question,
              timestamp: formatTimestamp(item.response.response_time),
            })
          );
          newMessages.push(...questionMessages);
        }
      });

      setMessages(newMessages);
    }
  }, [projectData]);

  const addMessage = (message: any) => {
    setMessages((prevMessages) => [...prevMessages, message]);
  };

  const handleSendMessage = async (content: string, title = '') => {
    if (!content || isProcessing) return;
    setIsProcessing(true);
    setIsLongResponse(false);

    // Clear any existing timers
    if (longResponseTimer) {
      clearTimeout(longResponseTimer);
      setLongResponseTimer(null);
    }

    // Add user message
    addMessage({
      sender: 'user',
      content,
      type: 'light',
      image: newProfileImage || senderIcon,
      timestamp: new Date().toLocaleTimeString(),
    });

    // Add loading message
    const loadingMessageId = Date.now();
    addMessage({
      id: loadingMessageId,
      sender: 'bot',
      content: '✍️ Analyzing your request...',
      type: 'light',
      image: botIcon,
      timestamp: new Date().toLocaleTimeString(),
    });

    // Set up a timer to detect long responses (after 10 seconds)
    const timer = setTimeout(() => {
      setIsLongResponse(true);
      // Update the loading message to indicate a long response time
      toast(
        `This is taking longer than usual. I'm preparing a thorough response — please bear with me!`,
        {
          id: 'long-response',
          duration: 5000,
          icon: '🐢',
          style: {
            background: '#fff3cd', // light yellow
            color: '#856404', // dark yellow-brown text
            border: '1px solid #ffeeba', // yellow border
          },
        }
      );
      setMessages((prevMessages) =>
        prevMessages.map((msg) =>
          msg.id === loadingMessageId
            ? {
                ...msg,
                content: `This is taking longer than usual. I'm preparing a thorough response — please bear with me!...`,
                isLongResponse: true,
              }
            : msg
        )
      );
    }, 60000); // 60 seconds

    setLongResponseTimer(timer);

    const currentResponseType = localStorage.getItem('responseType') || 'brief';
    const storedPageRange = localStorage.getItem('pageRangeChanges');
    const currentPageRange =
      storedPageRange !== null && storedPageRange !== 'undefined'
        ? storedPageRange
        : pageRangeChanges || '';
    const dontGenerateChart =
      localStorage.getItem('dontGenerateChart') === 'true';

    // Extract token from local storage and remove any quotes
    const token = localStorage.getItem('token')?.replace(/['"]+/g, '');

    const fileExtension = getSimplifiedFileExtension(getFileType);
    const formData = createFormData({
      userId: userInLocalStorage?.id,
      projectId: projectID,
      pageNumber: currentPageRange,
      fileName: getFileName || '',
      fileType: fileExtension || '',
      title: getProjectTitle || title,
      visualization: dontGenerateChart,
      prompt: content,
      response_type: currentResponseType,
    });

    const streamMessageId = Date.now() + 1;
    setCurrentStreamingMessageId(streamMessageId);
    setInputValue('');

    try {
      const response = await fetch(`${AI_BASE_URL}/process/`, {
        method: 'POST',
        headers: {
          // Add Authorization header with token if available
          ...(token && { Authorization: `Bearer ${token}` }),
        },
        body: formData,
      });

      // Check if the response is successful
      if (!response.ok) {
        // Clear the long response timer immediately on error
        clearTimeout(timer);
        setLongResponseTimer(null);
        setIsLongResponse(false);
        // Dismiss any existing long response toast
        toast.dismiss('long-response');

        // Try to parse error response
        let errorMessage =
          'An error occurred while processing your request. Please try again.';
        try {
          const errorData = await response.json();
          if (errorData.detail) {
            errorMessage = errorData.detail;
          } else if (errorData.message) {
            errorMessage = errorData.message;
          }
        } catch (parseError) {
          console.error('Error parsing error response:', parseError);
          // Use status-based error messages if JSON parsing fails
          if (response.status === 429) {
            errorMessage =
              'Your account has reached its usage limit. Please contact your administrator to review your service plan.';
          } else if (response.status === 401) {
            errorMessage = 'Authentication failed. Please log in again.';
          } else if (response.status === 403) {
            errorMessage =
              'Access denied. You do not have permission to perform this action.';
          } else if (response.status >= 500) {
            errorMessage = 'Server error occurred. Please try again later.';
          }
        }

        // Handle the error with the specific message
        handleError(streamMessageId, errorMessage, loadingMessageId);
        return;
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No reader available');

      let streamContent = '';
      let decoder = new TextDecoder();
      let buffer = '';
      let isStreamMessageAdded = false;

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          // Stream is complete - ensure all loading states are cleared
          if (longResponseTimer) {
            clearTimeout(longResponseTimer);
            setLongResponseTimer(null);
          }
          setIsLongResponse(false);
          setLoadingFinalResponse(false);
          setCodeInput(false);
          // Dismiss any existing long response toast
          toast.dismiss('long-response');
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              setMessages((prevMessages) =>
                prevMessages.filter((msg) => msg.id !== loadingMessageId)
              );

              const jsonData = JSON.parse(line.slice(6));
              setCodeInput(false);
              setLoadingFinalResponse(false);

              handleStreamMessage(
                jsonData,
                streamMessageId,
                streamContent,
                isStreamMessageAdded
              );
              isStreamMessageAdded = true;

              if (jsonData.type === 'message') {
                streamContent += jsonData.content;
              }
            } catch (e) {
              console.error('Error parsing JSON:', e, line);
            }
          }
        }
      }
    } catch (error) {
      console.error('Error processing your request:', error);
      // Clear the long response timer on any error
      clearTimeout(timer);
      setLongResponseTimer(null);
      setIsLongResponse(false);
      // Dismiss any existing long response toast
      toast.dismiss('long-response');

      handleError(streamMessageId, undefined, loadingMessageId);
    } finally {
      setIsProcessing(false);
      // Clear the long response timer
      clearTimeout(timer);
      setLongResponseTimer(null);
      setIsLongResponse(false);
    }
  };

  const handleStreamMessage = (
    jsonData: any,
    streamMessageId: number,
    streamContent: string,
    isStreamMessageAdded: boolean
  ) => {
    // Clear the long response timer when we start receiving a response
    if (longResponseTimer) {
      clearTimeout(longResponseTimer);
      setLongResponseTimer(null);
    }
    setIsLongResponse(false);
    // Dismiss any existing long response toast
    toast.dismiss('long-response');

    if (!isStreamMessageAdded) {
      addMessage({
        id: streamMessageId,
        sender: 'bot',
        content: '',
        type: 'light',
        image: botIcon,
        timestamp: new Date().toLocaleTimeString(),
      });
    }

    switch (jsonData.type) {
      case 'complete':
        setLoadingFinalResponse(true);
        // Clear long response state when stream is complete
        setIsLongResponse(false);
        break;
      case 'code_input':
        setCodeInput(true);
        break;
      case 'message':
        updateStreamMessage(streamMessageId, streamContent);
        break;
      case 'final_response':
        handleFinalResponse(jsonData.content, streamMessageId, streamContent);
        break;
    }
  };

  const updateStreamMessage = (streamMessageId: number, content: string) => {
    setMessages((prevMessages) =>
      prevMessages.map((msg) =>
        msg.id === streamMessageId
          ? {
              ...msg,
              content,
              sender: 'bot',
              type: 'light',
              image: botIcon,
            }
          : msg
      )
    );
  };

  const handleFinalResponse = (
    finalResponse: any,
    streamMessageId: number,
    streamContent: string
  ) => {
    setCurrentStreamingMessageId(null);

    // Clear all loading states and timers when final response is received
    if (longResponseTimer) {
      clearTimeout(longResponseTimer);
      setLongResponseTimer(null);
    }
    setIsLongResponse(false);
    setLoadingFinalResponse(false);
    setCodeInput(false);

    // Dismiss any existing long response toast
    toast.dismiss('long-response');

    const newMessages: any[] = [];

    // Add the final streamed content
    newMessages.push({
      sender: 'bot',
      content: streamContent,
      type: 'light',
      image: botIcon,
      timestamp:
        formatTimestamp(finalResponse.response_time) ||
        new Date().toLocaleTimeString(),
      responseId: finalResponse.response_id,
    });

    // Handle tables
    if (finalResponse.tables?.length > 0) {
      const chartMessages = transformChartData(finalResponse.tables);
      newMessages.push(...chartMessages);
    }

    // Handle images
    if (finalResponse.images?.length > 0) {
      const chartImages = finalResponse.images.map((image: any) => ({
        sender: 'bot',
        content: image,
        type: 'chartImage',
        timestamp: new Date().toLocaleTimeString(),
      }));
      newMessages.push(...chartImages);
    }

    // Handle source and page information
    if (finalResponse.source_type) {
      const sourceMessage = {
        sender: 'bot',
        content:
          finalResponse.source_type === 'file' && finalResponse.page_number
            ? `Source: ${finalResponse.source_type} | Referenced Pages ${finalResponse.page_number}`
            : `Source: ${finalResponse.source_type}`,
        type: 'hint',
        timestamp:
          formatTimestamp(finalResponse.response_time) ||
          new Date().toLocaleTimeString(),
      };
      newMessages.push(sourceMessage);
    }

    // Handle suggested questions
    if (finalResponse.suggested_questions?.length > 0) {
      newMessages.push({
        type: 'hint',
        sender: 'bot',
        content: 'Suggested follow-up questions:',
        timestamp:
          formatTimestamp(finalResponse.response_time) ||
          new Date().toLocaleTimeString(),
      });

      const questionMessages = finalResponse.suggested_questions.map(
        (question: string) => ({
          handleOnClick: () => handleSendMessage(question),
          sender: 'bot',
          isSuggestedQuestions: true,
          content: question,
          type: 'dark',
          timestamp: new Date().toLocaleTimeString(),
        })
      );
      newMessages.push(...questionMessages);
    }

    // Update messages
    setMessages((prevMessages) => {
      const filteredMessages = prevMessages.filter(
        (msg) => msg.id !== streamMessageId
      );
      return [...filteredMessages, ...newMessages];
    });

    if (finalResponse.object_id) {
      onMongoProjectIdChange(finalResponse.object_id);
    }
  };

  const handleError = (
    streamMessageId: number,
    errorMessage?: string,
    loadingMessageId?: number
  ) => {
    // Determine the error message to display
    let displayMessage =
      errorMessage ||
      'An error occurred while processing your request. Please try again.';

    // Check if this is a usage limit error and provide more helpful messaging
    if (errorMessage) {
      displayMessage = `❌ ${errorMessage}\n\n`;
    }
    setMessages((prevMessages) => {
      // If we have a loadingMessageId, remove the loading message and add error message
      if (loadingMessageId !== undefined) {
        return prevMessages.map((msg) =>
          msg.id === loadingMessageId
            ? {
                ...msg,
                content: displayMessage,
                sender: 'bot',
                type: 'light',
                image: botIcon,
              }
            : msg
        );
      } else {
        // Fallback: try to update streamMessageId (for cases where stream has started)
        return prevMessages.map((msg) =>
          msg.id === streamMessageId
            ? {
                ...msg,
                content: displayMessage,
                sender: 'bot',
                type: 'light',
                image: botIcon,
              }
            : msg
        );
      }
    });

    // Also show a toast notification for usage limit errors
    if (errorMessage) {
      toast.error('❌ ' + errorMessage, {
        id: 'usage-limit-error',
        duration: 8000,
        icon: '💳',
        style: {
          background: '#fee2e2', // light red
          color: '#991b1b', // dark red text
          border: '1px solid #fecaca', // red border
        },
      });
    }
  };

  return {
    messages,
    setMessages,
    isProcessing,
    inputValue,
    setInputValue,
    currentStreamingMessageId,
    loadingFinalResponse,
    codeInput,
    isLongResponse,
    handleSendMessage,
    addMessage,
  };
};
